<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>إنشاء حساب جديد - دليل قطع الغيار</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            min-height: 100vh;
            padding: 20px 20px 40px;
            color: #333;
        }
        
        .register-container {
            background: white;
            border-radius: 24px;
            padding: 30px 25px;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0C55AA, #3D73C4, #0C55AA);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            box-shadow: 0 8px 25px rgba(12, 85, 170, 0.3);
        }
        
        .logo svg {
            width: 35px;
            height: 35px;
            color: white;
        }
        
        .app-title {
            font-size: 22px;
            font-weight: 700;
            color: #0C55AA;
            margin-bottom: 6px;
        }
        
        .app-subtitle {
            font-size: 13px;
            color: #666;
        }
        
        .customer-type-selection {
            margin-bottom: 25px;
        }
        
        .customer-type-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            text-align: center;
        }
        
        .customer-types {
            display: flex;
            gap: 12px;
        }
        
        .customer-type {
            flex: 1;
            position: relative;
        }
        
        .customer-type input[type="radio"] {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .customer-type-label {
            display: block;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
        }
        
        .customer-type-label:hover {
            border-color: #0C55AA;
            background: #f0f7ff;
        }
        
        .customer-type input[type="radio"]:checked + .customer-type-label {
            border-color: #0C55AA;
            background: #e7f3ff;
            color: #0C55AA;
            font-weight: 600;
        }
        
        .customer-type-icon {
            font-size: 24px;
            margin-bottom: 6px;
        }
        
        .customer-type-text {
            font-size: 12px;
            font-weight: 500;
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
        }
        
        .form-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 15px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #0C55AA;
            background: white;
            box-shadow: 0 0 0 3px rgba(12, 85, 170, 0.1);
        }
        
        .form-input.error {
            border-color: #dc3545;
            background: #fff5f5;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 11px;
            margin-top: 4px;
            display: none;
        }
        
        .wholesale-fields {
            display: none;
            background: #fff8e1;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding: 0 16px;
            }
            to {
                opacity: 1;
                max-height: 300px;
                padding: 16px;
            }
        }
        
        .wholesale-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 12px;
            text-align: center;
        }
        
        .register-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(12, 85, 170, 0.4);
        }
        
        .register-btn:active {
            transform: translateY(0);
        }
        
        .register-btn.loading {
            pointer-events: none;
            opacity: 0.8;
        }
        
        .register-btn .spinner {
            display: none;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        .register-btn.loading .spinner {
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #666;
            font-size: 13px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
            z-index: 1;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }
        
        .login-link {
            text-align: center;
            margin-top: 16px;
        }
        
        .login-link a {
            color: #0C55AA;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .login-link a:hover {
            color: #3D73C4;
            text-decoration: underline;
        }
        
        .back-btn {
            position: fixed;
            top: 30px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }
        
        .back-btn:hover {
            background: white;
            transform: scale(1.1);
        }
        
        .back-btn svg {
            width: 20px;
            height: 20px;
            color: #0C55AA;
        }
        
        @media (max-width: 480px) {
            body {
                padding: 15px 15px 30px;
            }
            
            .register-container {
                padding: 25px 20px;
            }
            
            .app-title {
                font-size: 20px;
            }
            
            .customer-types {
                flex-direction: column;
                gap: 8px;
            }
            
            .customer-type-label {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m12 19-7-7 7-7"/>
            <path d="M19 12H5"/>
        </svg>
    </button>

    <div class="register-container">
        <div class="logo-section">
            <div class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                    <path d="m2 17 10 5 10-5"/>
                    <path d="m2 12 10 5 10-5"/>
                </svg>
            </div>
            <h1 class="app-title">دليل قطع الغيار</h1>
            <p class="app-subtitle">إنشاء حساب جديد</p>
        </div>

        <!-- Customer Type Selection -->
        <div class="customer-type-selection">
            <div class="customer-type-title">نوع العضوية</div>
            <div class="customer-types">
                <div class="customer-type">
                    <input type="radio" name="customer_type" value="individual" id="individual" checked>
                    <label for="individual" class="customer-type-label">
                        <div class="customer-type-icon">👤</div>
                        <div class="customer-type-text">عميل مفرد</div>
                    </label>
                </div>
                <div class="customer-type">
                    <input type="radio" name="customer_type" value="wholesale" id="wholesale">
                    <label for="wholesale" class="customer-type-label">
                        <div class="customer-type-icon">🏢</div>
                        <div class="customer-type-text">عميل جملة</div>
                    </label>
                </div>
            </div>
        </div>

        <form id="registerForm">
            <div class="form-group">
                <label class="form-label" for="name">الاسم الكامل</label>
                <input type="text" id="name" name="name" class="form-input" placeholder="أدخل اسمك الكامل" required>
                <div class="error-message" id="name-error"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" class="form-input" placeholder="أدخل بريدك الإلكتروني" required>
                <div class="error-message" id="email-error"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="phone">رقم الهاتف</label>
                <input type="tel" id="phone" name="phone" class="form-input" placeholder="أدخل رقم هاتفك" required>
                <div class="error-message" id="phone-error"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="أدخل كلمة المرور" required>
                <div class="error-message" id="password-error"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password_confirmation">تأكيد كلمة المرور</label>
                <input type="password" id="password_confirmation" name="password_confirmation" class="form-input" placeholder="أعد إدخال كلمة المرور" required>
                <div class="error-message" id="password_confirmation-error"></div>
            </div>

            <!-- Wholesale Fields -->
            <div id="wholesale-fields" class="wholesale-fields">
                <div class="wholesale-note">
                    <strong>ملاحظة:</strong> سيتم مراجعة طلبك من قبل الإدارة وستحصل على أسعار الجملة بعد الموافقة.
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="address">العنوان التفصيلي</label>
                    <input type="text" id="address" name="address" class="form-input" placeholder="أدخل عنوانك التفصيلي">
                    <div class="error-message" id="address-error"></div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="city">المدينة</label>
                    <input type="text" id="city" name="city" class="form-input" placeholder="أدخل المدينة">
                    <div class="error-message" id="city-error"></div>
                </div>
            </div>

            <button type="submit" class="register-btn">
                <span class="spinner"></span>
                إنشاء الحساب
            </button>
        </form>

        <div class="divider">
            <span>أو</span>
        </div>

        <div class="login-link">
            <p>لديك حساب بالفعل؟ <a href="mobile-login.html">تسجيل الدخول</a></p>
        </div>
    </div>

    <script>
        // Customer type toggle
        document.querySelectorAll('input[name="customer_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const wholesaleFields = document.getElementById('wholesale-fields');
                const wholesaleInputs = wholesaleFields.querySelectorAll('input');
                
                if (this.value === 'wholesale') {
                    wholesaleFields.style.display = 'block';
                    wholesaleInputs.forEach(input => {
                        input.setAttribute('required', 'required');
                    });
                } else {
                    wholesaleFields.style.display = 'none';
                    wholesaleInputs.forEach(input => {
                        input.removeAttribute('required');
                        input.value = '';
                    });
                }
            });
        });

        // Form handling
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const btn = document.querySelector('.register-btn');
            const formData = new FormData(this);
            
            // Clear previous errors
            clearErrors();
            
            // Validate
            if (!validateForm()) {
                return;
            }
            
            // Show loading
            btn.classList.add('loading');
            
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Success - show success message and redirect
                alert('تم إنشاء الحساب بنجاح! سيتم توجيهك لتسجيل الدخول.');
                window.location.href = 'mobile-login.html';
                
            } catch (error) {
                showError('email', 'حدث خطأ في إنشاء الحساب');
            } finally {
                btn.classList.remove('loading');
            }
        });
        
        function validateForm() {
            let isValid = true;
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            const passwordConfirmation = document.getElementById('password_confirmation').value;
            const customerType = document.querySelector('input[name="customer_type"]:checked').value;
            
            if (!name) {
                showError('name', 'يرجى إدخال الاسم الكامل');
                isValid = false;
            }
            
            if (!email) {
                showError('email', 'يرجى إدخال البريد الإلكتروني');
                isValid = false;
            } else if (!isValidEmail(email)) {
                showError('email', 'يرجى إدخال بريد إلكتروني صحيح');
                isValid = false;
            }
            
            if (!phone) {
                showError('phone', 'يرجى إدخال رقم الهاتف');
                isValid = false;
            }
            
            if (!password) {
                showError('password', 'يرجى إدخال كلمة المرور');
                isValid = false;
            } else if (password.length < 6) {
                showError('password', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                isValid = false;
            }
            
            if (password !== passwordConfirmation) {
                showError('password_confirmation', 'كلمة المرور غير متطابقة');
                isValid = false;
            }
            
            // Validate wholesale fields if selected
            if (customerType === 'wholesale') {
                const address = document.getElementById('address').value;
                const city = document.getElementById('city').value;
                
                if (!address) {
                    showError('address', 'يرجى إدخال العنوان التفصيلي');
                    isValid = false;
                }
                
                if (!city) {
                    showError('city', 'يرجى إدخال المدينة');
                    isValid = false;
                }
            }
            
            return isValid;
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showError(field, message) {
            const input = document.getElementById(field);
            const errorDiv = document.getElementById(field + '-error');
            
            input.classList.add('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function clearErrors() {
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error');
            });
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
            });
        }
        
        function goBack() {
            if (document.referrer) {
                window.history.back();
            } else {
                window.location.href = 'mobile-app.html';
            }
        }
        
        // Clear errors on input
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('input', function() {
                this.classList.remove('error');
                const errorDiv = document.getElementById(this.id + '-error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
