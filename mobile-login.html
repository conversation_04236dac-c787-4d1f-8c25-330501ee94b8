<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>تسجيل الدخول - دليل قطع الغيار</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #333;
        }
        
        .login-container {
            background: white;
            border-radius: 24px;
            padding: 40px 30px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0C55AA, #3D73C4, #0C55AA);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(12, 85, 170, 0.3);
        }
        
        .logo svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: 700;
            color: #0C55AA;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #0C55AA;
            background: white;
            box-shadow: 0 0 0 4px rgba(12, 85, 170, 0.1);
        }
        
        .form-input.error {
            border-color: #dc3545;
            background: #fff5f5;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 6px;
            display: none;
        }
        
        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(12, 85, 170, 0.4);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .login-btn.loading {
            pointer-events: none;
            opacity: 0.8;
        }
        
        .login-btn .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        .login-btn.loading .spinner {
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #666;
            font-size: 14px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
            z-index: 1;
        }
        
        .divider span {
            background: white;
            padding: 0 20px;
            position: relative;
            z-index: 2;
        }
        
        .register-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .register-link a {
            color: #0C55AA;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .register-link a:hover {
            color: #3D73C4;
            text-decoration: underline;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 16px;
        }
        
        .forgot-password a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: #0C55AA;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn:hover {
            background: white;
            transform: scale(1.1);
        }
        
        .back-btn svg {
            width: 20px;
            height: 20px;
            color: #0C55AA;
        }
        
        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .app-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m12 19-7-7 7-7"/>
            <path d="M19 12H5"/>
        </svg>
    </button>

    <div class="login-container">
        <div class="logo-section">
            <div class="logo">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                    <path d="m2 17 10 5 10-5"/>
                    <path d="m2 12 10 5 10-5"/>
                </svg>
            </div>
            <h1 class="app-title">دليل قطع الغيار</h1>
            <p class="app-subtitle">مرحباً بك مرة أخرى</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="email">البريد الإلكتروني أو رقم الهاتف</label>
                <input type="text" id="email" name="email" class="form-input" placeholder="أدخل بريدك الإلكتروني أو رقم هاتفك" required>
                <div class="error-message" id="email-error"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="أدخل كلمة المرور" required>
                <div class="error-message" id="password-error"></div>
            </div>

            <button type="submit" class="login-btn">
                <span class="spinner"></span>
                تسجيل الدخول
            </button>
        </form>

        <div class="forgot-password">
            <a href="mobile-forgot-password.html">نسيت كلمة المرور؟</a>
        </div>

        <div class="divider">
            <span>أو</span>
        </div>

        <div class="register-link">
            <p>ليس لديك حساب؟ <a href="mobile-register.html">إنشاء حساب جديد</a></p>
        </div>
    </div>

    <script>
        // Form handling
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const btn = document.querySelector('.login-btn');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // Clear previous errors
            clearErrors();
            
            // Validate
            if (!email) {
                showError('email', 'يرجى إدخال البريد الإلكتروني أو رقم الهاتف');
                return;
            }
            
            if (!password) {
                showError('password', 'يرجى إدخال كلمة المرور');
                return;
            }
            
            // Show loading
            btn.classList.add('loading');
            
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Success - redirect to app
                window.location.href = 'mobile-app.html';
                
            } catch (error) {
                showError('email', 'بيانات الدخول غير صحيحة');
            } finally {
                btn.classList.remove('loading');
            }
        });
        
        function showError(field, message) {
            const input = document.getElementById(field);
            const errorDiv = document.getElementById(field + '-error');
            
            input.classList.add('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function clearErrors() {
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error');
            });
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
            });
        }
        
        function goBack() {
            if (document.referrer) {
                window.history.back();
            } else {
                window.location.href = 'mobile-app.html';
            }
        }
        
        // Clear errors on input
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('input', function() {
                this.classList.remove('error');
                const errorDiv = document.getElementById(this.id + '-error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
