<?php
// Simple endpoint to return user authentication status
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, Authorization, X-CSRF-TOKEN');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if we can access Laravel
$laravelPath = dirname(dirname(dirname(dirname(__FILE__)))) . '/index.php';

if (file_exists($laravelPath)) {
    try {
        // Try to bootstrap Laravel
        require_once $laravelPath;
        
        // Check if user is authenticated
        if (function_exists('auth') && auth()->check()) {
            $user = auth()->user();
            
            echo json_encode([
                'error' => false,
                'data' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->avatar_url ?? null,
                    'is_authenticated' => true
                ]
            ]);
            exit;
        }
    } catch (Exception $e) {
        // Laravel bootstrap failed, continue to fallback
        error_log('Laravel bootstrap failed: ' . $e->getMessage());
    }
}

// Return not authenticated response
echo json_encode([
    'error' => true,
    'message' => 'User not authenticated',
    'data' => [
        'is_authenticated' => false
    ]
]);
?>
