RewriteEngine On

# Handle user authentication requests
RewriteRule ^user$ user.php [L]

# Set proper headers for API requests
<FilesMatch "\.(php)$">
    Header always set Content-Type "application/json; charset=utf-8"
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, X-Requested-With, Authorization, X-CSRF-TOKEN"
</FilesMatch>
