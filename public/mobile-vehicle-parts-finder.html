<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث عن قطع الغيار - دليل قطع الغيار</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليل قطع الغيار">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/mobile-pwa-enhancements.css">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            min-height: 100vh;
            color: white;
        }
        
        .vpf-container {
            padding: 20px;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .vpf-header {
            text-align: center;
            margin-bottom: 30px;
            padding-top: 40px;
        }
        
        .vpf-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
        }
        
        .vpf-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .vpf-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .vpf-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 16px;
        }
        
        .form-select {
            width: 100%;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .form-select option {
            background: #0C55AA;
            color: white;
        }
        
        .form-select:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .search-button {
            width: 100%;
            padding: 18px;
            background: white;
            color: #0C55AA;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .search-button:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .search-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .quick-search {
            margin-bottom: 30px;
        }
        
        .quick-search-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .quick-search-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .quick-search-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 20px 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        
        .quick-search-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .quick-search-icon {
            font-size: 30px;
            margin-bottom: 10px;
            display: block;
        }
        
        .quick-search-text {
            font-size: 14px;
            font-weight: 500;
        }
        
        .saved-vehicles {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .saved-vehicles-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .saved-vehicle-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .saved-vehicle-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .saved-vehicle-info {
            flex: 1;
        }
        
        .saved-vehicle-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .saved-vehicle-details {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .saved-vehicle-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
            color: #ffebee;
        }
        
        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
            color: #e8f5e8;
        }
        
        @media (max-width: 480px) {
            .vpf-container {
                padding: 15px;
            }
            
            .vpf-form {
                padding: 20px 15px;
            }
            
            .quick-search-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="vpf-container">
        <!-- Header -->
        <div class="vpf-header">
            <div class="vpf-logo">🚗</div>
            <h1 class="vpf-title">البحث عن قطع الغيار</h1>
            <p class="vpf-subtitle">اختر سيارتك للعثور على القطع المناسبة</p>
        </div>

        <!-- Main Search Form -->
        <div class="vpf-form">
            <form id="vpf-search-form">
                <div class="form-group">
                    <label class="form-label" for="vehicle-type">نوع المركبة</label>
                    <select class="form-select" id="vehicle-type" name="vehicle_type" required>
                        <option value="">اختر نوع المركبة</option>
                        <option value="car">سيارة</option>
                        <option value="truck">شاحنة</option>
                        <option value="motorcycle">دراجة نارية</option>
                        <option value="bus">حافلة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="vehicle-brand">الماركة</label>
                    <select class="form-select" id="vehicle-brand" name="vehicle_brand" disabled>
                        <option value="">اختر الماركة أولاً</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="vehicle-model">الموديل</label>
                    <select class="form-select" id="vehicle-model" name="vehicle_model" disabled>
                        <option value="">اختر الموديل</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="vehicle-year">السنة</label>
                    <select class="form-select" id="vehicle-year" name="vehicle_year" disabled>
                        <option value="">اختر السنة</option>
                    </select>
                </div>

                <button type="submit" class="search-button" id="search-btn">
                    <span>🔍</span>
                    البحث عن القطع
                </button>
            </form>
        </div>

        <!-- Quick Search -->
        <div class="quick-search">
            <h3 class="quick-search-title">البحث السريع</h3>
            <div class="quick-search-grid">
                <a href="/products?category=engine" class="quick-search-item">
                    <span class="quick-search-icon">⚙️</span>
                    <span class="quick-search-text">قطع المحرك</span>
                </a>
                <a href="/products?category=brakes" class="quick-search-item">
                    <span class="quick-search-icon">🛑</span>
                    <span class="quick-search-text">نظام الفرامل</span>
                </a>
                <a href="/products?category=suspension" class="quick-search-item">
                    <span class="quick-search-icon">🔧</span>
                    <span class="quick-search-text">نظام التعليق</span>
                </a>
                <a href="/products?category=electrical" class="quick-search-item">
                    <span class="quick-search-icon">⚡</span>
                    <span class="quick-search-text">القطع الكهربائية</span>
                </a>
            </div>
        </div>

        <!-- Saved Vehicles -->
        <div class="saved-vehicles" id="saved-vehicles" style="display: none;">
            <h3 class="saved-vehicles-title">سياراتي المحفوظة</h3>
            <div id="saved-vehicles-list">
                <!-- Saved vehicles will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/mobile-pwa-features.js"></script>
    <script src="/js/advanced-mobile-features.js"></script>
    
    <script>
        // Vehicle Parts Finder Logic
        class VehiclePartsFinder {
            constructor() {
                this.apiBase = '/api/v1/vehicle-parts-finder';
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadSavedVehicles();
            }

            setupEventListeners() {
                document.getElementById('vehicle-type').addEventListener('change', (e) => {
                    this.loadBrands(e.target.value);
                });

                document.getElementById('vehicle-brand').addEventListener('change', (e) => {
                    this.loadModels(e.target.value);
                });

                document.getElementById('vehicle-model').addEventListener('change', (e) => {
                    this.loadYears(e.target.value);
                });

                document.getElementById('vpf-search-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.performSearch();
                });
            }

            async loadBrands(vehicleType) {
                if (!vehicleType) return;

                const brandSelect = document.getElementById('vehicle-brand');
                const modelSelect = document.getElementById('vehicle-model');
                const yearSelect = document.getElementById('vehicle-year');

                // Reset dependent selects
                modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
                yearSelect.innerHTML = '<option value="">اختر السنة</option>';
                modelSelect.disabled = true;
                yearSelect.disabled = true;

                try {
                    brandSelect.innerHTML = '<option value="">جاري التحميل...</option>';
                    brandSelect.disabled = true;

                    const response = await fetch(`${this.apiBase}/brands?type=${vehicleType}`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    brandSelect.innerHTML = '<option value="">اختر الماركة</option>';
                    data.data.forEach(brand => {
                        brandSelect.innerHTML += `<option value="${brand.id}">${brand.name}</option>`;
                    });

                    brandSelect.disabled = false;
                } catch (error) {
                    console.error('Error loading brands:', error);
                    brandSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
                }
            }

            async loadModels(brandId) {
                if (!brandId) return;

                const modelSelect = document.getElementById('vehicle-model');
                const yearSelect = document.getElementById('vehicle-year');

                yearSelect.innerHTML = '<option value="">اختر السنة</option>';
                yearSelect.disabled = true;

                try {
                    modelSelect.innerHTML = '<option value="">جاري التحميل...</option>';
                    modelSelect.disabled = true;

                    const response = await fetch(`${this.apiBase}/models?brand=${brandId}`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    modelSelect.innerHTML = '<option value="">اختر الموديل</option>';
                    data.data.forEach(model => {
                        modelSelect.innerHTML += `<option value="${model.id}">${model.name}</option>`;
                    });

                    modelSelect.disabled = false;
                } catch (error) {
                    console.error('Error loading models:', error);
                    modelSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
                }
            }

            async loadYears(modelId) {
                if (!modelId) return;

                const yearSelect = document.getElementById('vehicle-year');

                try {
                    yearSelect.innerHTML = '<option value="">جاري التحميل...</option>';
                    yearSelect.disabled = true;

                    const response = await fetch(`${this.apiBase}/years?model=${modelId}`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    yearSelect.innerHTML = '<option value="">اختر السنة</option>';
                    data.data.forEach(year => {
                        yearSelect.innerHTML += `<option value="${year.id}">${year.name}</option>`;
                    });

                    yearSelect.disabled = false;
                } catch (error) {
                    console.error('Error loading years:', error);
                    yearSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
                }
            }

            async performSearch() {
                const formData = new FormData(document.getElementById('vpf-search-form'));
                const searchBtn = document.getElementById('search-btn');

                // Validate form
                const vehicleType = formData.get('vehicle_type');
                if (!vehicleType) {
                    this.showMessage('يرجى اختيار نوع المركبة على الأقل', 'error');
                    return;
                }

                try {
                    searchBtn.classList.add('loading');
                    searchBtn.disabled = true;

                    // Save vehicle if all fields are selected
                    if (formData.get('vehicle_year')) {
                        this.saveVehicle(formData);
                    }

                    // Redirect to search results
                    const params = new URLSearchParams(formData);
                    window.location.href = `/products?${params.toString()}`;

                } catch (error) {
                    console.error('Search error:', error);
                    this.showMessage('حدث خطأ في البحث', 'error');
                } finally {
                    searchBtn.classList.remove('loading');
                    searchBtn.disabled = false;
                }
            }

            saveVehicle(formData) {
                const vehicles = JSON.parse(localStorage.getItem('saved_vehicles') || '[]');
                const newVehicle = {
                    id: Date.now(),
                    type: formData.get('vehicle_type'),
                    brand: formData.get('vehicle_brand'),
                    model: formData.get('vehicle_model'),
                    year: formData.get('vehicle_year'),
                    saved_at: new Date().toISOString()
                };

                // Check if vehicle already exists
                const exists = vehicles.some(v => 
                    v.type === newVehicle.type && 
                    v.brand === newVehicle.brand && 
                    v.model === newVehicle.model && 
                    v.year === newVehicle.year
                );

                if (!exists) {
                    vehicles.push(newVehicle);
                    localStorage.setItem('saved_vehicles', JSON.stringify(vehicles));
                    this.loadSavedVehicles();
                }
            }

            loadSavedVehicles() {
                const vehicles = JSON.parse(localStorage.getItem('saved_vehicles') || '[]');
                const container = document.getElementById('saved-vehicles');
                const list = document.getElementById('saved-vehicles-list');

                if (vehicles.length === 0) {
                    container.style.display = 'none';
                    return;
                }

                container.style.display = 'block';
                list.innerHTML = '';

                vehicles.forEach(vehicle => {
                    const item = document.createElement('div');
                    item.className = 'saved-vehicle-item';
                    item.innerHTML = `
                        <div class="saved-vehicle-info">
                            <div class="saved-vehicle-name">${vehicle.brand} ${vehicle.model}</div>
                            <div class="saved-vehicle-details">${vehicle.type} - ${vehicle.year}</div>
                        </div>
                        <div class="saved-vehicle-actions">
                            <button class="action-btn" onclick="vpf.selectVehicle(${vehicle.id})" title="اختيار">✓</button>
                            <button class="action-btn" onclick="vpf.deleteVehicle(${vehicle.id})" title="حذف">🗑️</button>
                        </div>
                    `;
                    list.appendChild(item);
                });
            }

            selectVehicle(vehicleId) {
                const vehicles = JSON.parse(localStorage.getItem('saved_vehicles') || '[]');
                const vehicle = vehicles.find(v => v.id === vehicleId);
                
                if (vehicle) {
                    // Fill form with saved vehicle data
                    document.getElementById('vehicle-type').value = vehicle.type;
                    this.loadBrands(vehicle.type).then(() => {
                        document.getElementById('vehicle-brand').value = vehicle.brand;
                        this.loadModels(vehicle.brand).then(() => {
                            document.getElementById('vehicle-model').value = vehicle.model;
                            this.loadYears(vehicle.model).then(() => {
                                document.getElementById('vehicle-year').value = vehicle.year;
                            });
                        });
                    });
                }
            }

            deleteVehicle(vehicleId) {
                if (confirm('هل تريد حذف هذه السيارة من القائمة المحفوظة؟')) {
                    const vehicles = JSON.parse(localStorage.getItem('saved_vehicles') || '[]');
                    const filtered = vehicles.filter(v => v.id !== vehicleId);
                    localStorage.setItem('saved_vehicles', JSON.stringify(filtered));
                    this.loadSavedVehicles();
                }
            }

            showMessage(message, type) {
                const existing = document.querySelector('.error-message, .success-message');
                if (existing) existing.remove();

                const messageDiv = document.createElement('div');
                messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
                messageDiv.textContent = message;

                document.querySelector('.vpf-form').appendChild(messageDiv);

                setTimeout(() => {
                    messageDiv.remove();
                }, 5000);
            }
        }

        // Initialize VPF
        const vpf = new VehiclePartsFinder();
    </script>
</body>
</html>
