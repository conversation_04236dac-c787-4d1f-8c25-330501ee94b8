<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - دليل قطع الغيار</title>
    <meta name="description" content="إنشاء حساب جديد في دليل قطع الغيار للسيارات">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليل قطع الغيار">
    
    <!-- Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/pwa/icon-192x192.png">
    <link rel="manifest" href="/pwa/manifest.json">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            min-height: 100vh;
            padding: 20px 0;
            direction: rtl;
        }

        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            margin: 0 auto;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .register-header {
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }

        .register-header h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .register-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .register-form {
            padding: 30px 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .customer-type-selector {
            margin-bottom: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e0e0e0;
        }

        .customer-type-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .customer-type-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .customer-type-option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .customer-type-option:hover {
            border-color: #0C55AA;
            transform: translateY(-2px);
        }

        .customer-type-option.active {
            border-color: #0C55AA;
            background: #f0f7ff;
        }

        .customer-type-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }

        .customer-type-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .customer-type-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .customer-type-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-label.required::after {
            content: ' *';
            color: #e74c3c;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #0C55AA;
            background: white;
            box-shadow: 0 0 0 3px rgba(12, 85, 170, 0.1);
        }

        .form-input::placeholder {
            color: #999;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: #666;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 12px;
        }

        .strength-bar {
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            margin: 4px 0;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #e74c3c; width: 25%; }
        .strength-fair { background: #f39c12; width: 50%; }
        .strength-good { background: #f1c40f; width: 75%; }
        .strength-strong { background: #27ae60; width: 100%; }

        .wholesale-fields {
            display: none;
            background: #fff8e1;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .wholesale-fields.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding: 0 20px;
            }
            to {
                opacity: 1;
                max-height: 500px;
                padding: 20px;
            }
        }

        .wholesale-title {
            font-size: 16px;
            font-weight: 600;
            color: #f57c00;
            margin-bottom: 15px;
            text-align: center;
        }

        .wholesale-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            font-size: 13px;
            color: #856404;
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 25px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .terms-checkbox input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #0C55AA;
            margin-top: 2px;
        }

        .terms-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .terms-text a {
            color: #0C55AA;
            text-decoration: none;
            font-weight: 600;
        }

        .terms-text a:hover {
            text-decoration: underline;
        }

        .register-button {
            width: 100%;
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .register-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(12, 85, 170, 0.3);
        }

        .register-button:active {
            transform: translateY(0);
        }

        .register-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .login-link p {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .login-button {
            color: #0C55AA;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
        }

        .login-button:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #fcc;
        }

        .success-message {
            background: #efe;
            color: #363;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #cfc;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0C55AA;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .register-container {
                margin: 0 10px;
                border-radius: 15px;
            }
            
            .register-header {
                padding: 25px 15px;
            }
            
            .register-form {
                padding: 25px 15px;
            }
            
            .customer-type-options {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <button class="back-button" onclick="goBack()">←</button>
            <h1>إنشاء حساب جديد</h1>
            <p>انضم إلى دليل قطع الغيار</p>
        </div>

        <div class="register-form">
            <div id="message-container"></div>
            
            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <p>جاري إنشاء الحساب...</p>
            </div>

            <form id="registerForm" style="display: block;">
                <!-- Customer Type Selection -->
                <div class="customer-type-selector">
                    <div class="customer-type-title">نوع العضوية</div>
                    <div class="customer-type-options">
                        <div class="customer-type-option active" onclick="selectCustomerType('individual')">
                            <input type="radio" name="customer_type" value="individual" checked>
                            <div class="customer-type-icon">👤</div>
                            <div class="customer-type-label">عميل مفرد</div>
                            <div class="customer-type-desc">للأفراد والاستخدام الشخصي</div>
                        </div>
                        <div class="customer-type-option" onclick="selectCustomerType('wholesale')">
                            <input type="radio" name="customer_type" value="wholesale">
                            <div class="customer-type-icon">🏢</div>
                            <div class="customer-type-label">عميل جملة</div>
                            <div class="customer-type-desc">للشركات والتجار</div>
                        </div>
                    </div>
                </div>

                <!-- Basic Information -->
                <div class="form-group">
                    <label for="name" class="form-label required">الاسم الكامل</label>
                    <input type="text" id="name" name="name" class="form-input" 
                           placeholder="أدخل اسمك الكامل" required>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label required">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" class="form-input" 
                           placeholder="أدخل بريدك الإلكتروني" required>
                </div>

                <div class="form-group">
                    <label for="phone" class="form-label required">رقم الهاتف</label>
                    <input type="tel" id="phone" name="phone" class="form-input" 
                           placeholder="أدخل رقم هاتفك" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password" class="form-label required">كلمة المرور</label>
                        <div class="password-container">
                            <input type="password" id="password" name="password" class="form-input" 
                                   placeholder="أدخل كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">👁️</button>
                        </div>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strength-fill"></div>
                            </div>
                            <div id="strength-text">قوة كلمة المرور</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="password_confirmation" class="form-label required">تأكيد كلمة المرور</label>
                        <div class="password-container">
                            <input type="password" id="password_confirmation" name="password_confirmation" class="form-input" 
                                   placeholder="أعد إدخال كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">👁️</button>
                        </div>
                    </div>
                </div>

                <!-- Wholesale Fields -->
                <div class="wholesale-fields" id="wholesale-fields">
                    <div class="wholesale-title">🏢 معلومات عميل الجملة</div>
                    <div class="wholesale-note">
                        <strong>ملاحظة:</strong> سيتم مراجعة طلبك من قبل الإدارة وستحصل على أسعار الجملة بعد الموافقة.
                    </div>
                    
                    <div class="form-group">
                        <label for="address" class="form-label required">العنوان التفصيلي</label>
                        <input type="text" id="address" name="address" class="form-input" 
                               placeholder="أدخل عنوانك التفصيلي">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="city" class="form-label required">المدينة</label>
                            <input type="text" id="city" name="city" class="form-input" 
                                   placeholder="أدخل المدينة">
                        </div>
                        <div class="form-group">
                            <label for="state" class="form-label required">المنطقة/المحافظة</label>
                            <input type="text" id="state" name="state" class="form-input" 
                                   placeholder="أدخل المنطقة">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="zip_code" class="form-label">الرمز البريدي</label>
                        <input type="text" id="zip_code" name="zip_code" class="form-input" 
                               placeholder="أدخل الرمز البريدي (اختياري)">
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="terms-checkbox">
                    <input type="checkbox" id="agree_terms" name="agree_terms_and_policy" required>
                    <div class="terms-text">
                        أوافق على <a href="#" onclick="showTerms()">الشروط والأحكام</a> و 
                        <a href="#" onclick="showPrivacy()">سياسة الخصوصية</a>
                    </div>
                </div>

                <button type="submit" class="register-button" id="registerButton">
                    إنشاء الحساب
                </button>
            </form>
        </div>

        <div class="login-link">
            <p>لديك حساب بالفعل؟</p>
            <a href="/mobile-login.html" class="login-button">تسجيل الدخول</a>
        </div>
    </div>

    <script>
        // App Configuration
        const APP_CONFIG = {
            apiBase: '/api/v1',
            baseUrl: window.location.origin
        };

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeRegisterForm();
            setupPasswordStrength();
        });

        function initializeRegisterForm() {
            const form = document.getElementById('registerForm');
            form.addEventListener('submit', handleRegister);
        }

        function selectCustomerType(type) {
            // Update radio buttons
            const radios = document.querySelectorAll('input[name="customer_type"]');
            radios.forEach(radio => {
                radio.checked = radio.value === type;
            });

            // Update visual selection
            const options = document.querySelectorAll('.customer-type-option');
            options.forEach(option => {
                option.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // Show/hide wholesale fields
            const wholesaleFields = document.getElementById('wholesale-fields');
            if (type === 'wholesale') {
                wholesaleFields.classList.add('show');
                // Make wholesale fields required
                const wholesaleInputs = wholesaleFields.querySelectorAll('input[required]');
                wholesaleInputs.forEach(input => input.required = true);
            } else {
                wholesaleFields.classList.remove('show');
                // Make wholesale fields optional
                const wholesaleInputs = wholesaleFields.querySelectorAll('input');
                wholesaleInputs.forEach(input => input.required = false);
            }
        }

        function setupPasswordStrength() {
            const passwordInput = document.getElementById('password');
            passwordInput.addEventListener('input', checkPasswordStrength);
        }

        function checkPasswordStrength() {
            const password = document.getElementById('password').value;
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');

            let strength = 0;
            let text = 'ضعيفة';
            let className = 'strength-weak';

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            switch (strength) {
                case 0:
                case 1:
                    text = 'ضعيفة جداً';
                    className = 'strength-weak';
                    break;
                case 2:
                    text = 'ضعيفة';
                    className = 'strength-weak';
                    break;
                case 3:
                    text = 'متوسطة';
                    className = 'strength-fair';
                    break;
                case 4:
                    text = 'جيدة';
                    className = 'strength-good';
                    break;
                case 5:
                    text = 'قوية';
                    className = 'strength-strong';
                    break;
            }

            strengthFill.className = `strength-fill ${className}`;
            strengthText.textContent = text;
        }

        async function handleRegister(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            
            // Validate passwords match
            const password = formData.get('password');
            const passwordConfirmation = formData.get('password_confirmation');
            
            if (password !== passwordConfirmation) {
                showMessage('كلمات المرور غير متطابقة', 'error');
                return;
            }

            // Prepare data
            const customerType = formData.get('customer_type');
            const requestData = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                password: password,
                password_confirmation: passwordConfirmation,
                agree_terms_and_policy: formData.get('agree_terms_and_policy') ? 1 : 0,
                request_wholesale: customerType === 'wholesale' ? 1 : 0
            };

            // Add wholesale fields if needed
            if (customerType === 'wholesale') {
                requestData.address = formData.get('address');
                requestData.city = formData.get('city');
                requestData.state = formData.get('state');
                requestData.zip_code = formData.get('zip_code');
            }

            showLoading(true);

            try {
                const response = await fetch('/customer/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();

                if (response.ok && data.success !== false) {
                    // Registration successful
                    let message = 'تم إنشاء الحساب بنجاح!';
                    
                    if (customerType === 'wholesale') {
                        message += ' سيتم مراجعة طلب عضوية الجملة وإشعارك بالنتيجة.';
                    }
                    
                    showMessage(message + ' جاري التوجيه لتسجيل الدخول...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = '/mobile-login.html';
                    }, 2000);
                } else {
                    // Registration failed
                    const errorMessage = data.message || 'فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.';
                    showMessage(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Registration error:', error);
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                showLoading(false);
            }
        }

        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleButton = passwordInput.nextElementSibling;
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }

        function showTerms() {
            alert('سيتم إضافة الشروط والأحكام قريباً');
        }

        function showPrivacy() {
            alert('سيتم إضافة سياسة الخصوصية قريباً');
        }

        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const className = type === 'error' ? 'error-message' : 'success-message';
            
            container.innerHTML = `<div class="${className}">${message}</div>`;
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const form = document.getElementById('registerForm');
            const button = document.getElementById('registerButton');
            
            if (show) {
                loading.style.display = 'block';
                form.style.display = 'none';
                button.disabled = true;
            } else {
                loading.style.display = 'none';
                form.style.display = 'block';
                button.disabled = false;
            }
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/mobile-app.html';
            }
        }
    </script>
</body>
</html>
