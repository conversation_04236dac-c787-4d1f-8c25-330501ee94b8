/**
 * Advanced Mobile Features for Dalil Auto Parts PWA
 * Voice Search, QR Scanner, and Enhanced Mobile Interactions
 */

class AdvancedMobileFeatures {
    constructor() {
        this.isVoiceSearchSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
        this.isQRScannerSupported = 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
        this.recognition = null;
        this.qrScanner = null;
        this.currentStream = null;
        
        this.init();
    }

    init() {
        this.setupVoiceSearch();
        this.setupQRScanner();
        this.setupAdvancedGestures();
        this.setupShareAPI();
        this.setupHapticFeedback();
    }

    // ===================================
    // Voice Search Implementation
    // ===================================

    setupVoiceSearch() {
        if (!this.isVoiceSearchSupported) {
            console.log('Voice search not supported');
            return;
        }

        // Add voice search button to search bars
        document.querySelectorAll('.search-bar-mobile').forEach(searchBar => {
            this.addVoiceSearchButton(searchBar);
        });

        // Initialize speech recognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.lang = 'ar-SA'; // Arabic Saudi Arabia

        this.recognition.onstart = () => {
            console.log('Voice search started');
            this.showVoiceSearchModal();
        };

        this.recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            console.log('Voice search result:', transcript);
            this.handleVoiceSearchResult(transcript);
        };

        this.recognition.onerror = (event) => {
            console.error('Voice search error:', event.error);
            this.hideVoiceSearchModal();
            this.showToast('حدث خطأ في البحث الصوتي', 'error');
        };

        this.recognition.onend = () => {
            console.log('Voice search ended');
            this.hideVoiceSearchModal();
        };
    }

    addVoiceSearchButton(searchBar) {
        const voiceBtn = document.createElement('button');
        voiceBtn.className = 'voice-search-btn';
        voiceBtn.innerHTML = '🎤';
        voiceBtn.title = 'البحث الصوتي';
        voiceBtn.onclick = (e) => {
            e.preventDefault();
            this.startVoiceSearch();
        };

        searchBar.appendChild(voiceBtn);
    }

    startVoiceSearch() {
        if (!this.recognition) return;

        try {
            this.recognition.start();
            this.hapticFeedback('light');
        } catch (error) {
            console.error('Error starting voice search:', error);
            this.showToast('لا يمكن بدء البحث الصوتي', 'error');
        }
    }

    showVoiceSearchModal() {
        const modal = document.createElement('div');
        modal.className = 'voice-search-modal';
        modal.id = 'voice-search-modal';
        modal.innerHTML = `
            <div class="voice-search-content">
                <div class="voice-search-icon">🎤</div>
                <h3>جاري الاستماع...</h3>
                <p>تحدث الآن للبحث عن المنتجات</p>
                <button onclick="this.closest('.voice-search-modal').remove(); window.advancedFeatures.recognition.stop();" class="btn btn-secondary">إلغاء</button>
            </div>
        `;

        document.body.appendChild(modal);
        setTimeout(() => modal.classList.add('show'), 100);
    }

    hideVoiceSearchModal() {
        const modal = document.getElementById('voice-search-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        }
    }

    handleVoiceSearchResult(transcript) {
        // Fill search input with voice result
        const searchInput = document.querySelector('.search-input-mobile');
        if (searchInput) {
            searchInput.value = transcript;
            
            // Trigger search
            const searchForm = searchInput.closest('form');
            if (searchForm) {
                searchForm.submit();
            } else {
                // Manual search trigger
                this.performSearch(transcript);
            }
        }

        this.showToast(`البحث عن: ${transcript}`, 'success');
    }

    // ===================================
    // QR Scanner Implementation
    // ===================================

    setupQRScanner() {
        if (!this.isQRScannerSupported) {
            console.log('QR Scanner not supported');
            return;
        }

        // Add QR scanner button
        this.addQRScannerButton();
    }

    addQRScannerButton() {
        const qrBtn = document.createElement('button');
        qrBtn.className = 'btn-mobile-enhanced qr-scanner-btn';
        qrBtn.innerHTML = '📷 مسح QR';
        qrBtn.style.cssText = `
            position: fixed;
            bottom: 140px;
            right: 20px;
            z-index: 1000;
            background: var(--dalil-primary);
            color: white;
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 14px;
        `;
        qrBtn.onclick = () => this.startQRScanner();

        document.body.appendChild(qrBtn);
    }

    async startQRScanner() {
        try {
            this.currentStream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });

            this.showQRScannerModal();
            this.hapticFeedback('medium');
        } catch (error) {
            console.error('Error starting QR scanner:', error);
            this.showToast('لا يمكن الوصول للكاميرا', 'error');
        }
    }

    showQRScannerModal() {
        const modal = document.createElement('div');
        modal.className = 'qr-scanner-modal';
        modal.id = 'qr-scanner-modal';
        modal.innerHTML = `
            <div class="qr-scanner-header">
                <h3>مسح رمز QR</h3>
                <button class="qr-scanner-close" onclick="window.advancedFeatures.stopQRScanner()">✕</button>
            </div>
            <video class="qr-scanner-video" id="qr-video" autoplay playsinline></video>
            <div class="qr-scanner-overlay">
                <div class="qr-scanner-corners"></div>
            </div>
        `;

        document.body.appendChild(modal);
        
        const video = document.getElementById('qr-video');
        video.srcObject = this.currentStream;
        
        setTimeout(() => modal.classList.add('show'), 100);
        
        // Start QR detection
        this.startQRDetection(video);
    }

    startQRDetection(video) {
        // Simple QR detection simulation
        // In a real implementation, you would use a library like jsQR
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        const detectQR = () => {
            if (video.videoWidth > 0 && video.videoHeight > 0) {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0);
                
                // Here you would use jsQR or similar library
                // const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                // const code = jsQR(imageData.data, imageData.width, imageData.height);
                
                // Simulate QR detection for demo
                if (Math.random() < 0.01) { // 1% chance per frame
                    this.handleQRResult('DEMO_PRODUCT_123');
                    return;
                }
            }
            
            if (document.getElementById('qr-scanner-modal')) {
                requestAnimationFrame(detectQR);
            }
        };
        
        video.addEventListener('loadedmetadata', () => {
            detectQR();
        });
    }

    handleQRResult(qrData) {
        console.log('QR Code detected:', qrData);
        this.stopQRScanner();
        
        // Handle different QR code types
        if (qrData.startsWith('http')) {
            // URL QR code
            window.open(qrData, '_blank');
        } else if (qrData.includes('PRODUCT_')) {
            // Product QR code
            const productId = qrData.replace('DEMO_PRODUCT_', '');
            window.location.href = `/products/${productId}`;
        } else {
            // Generic QR code - search for it
            this.performSearch(qrData);
        }
        
        this.showToast('تم مسح الرمز بنجاح!', 'success');
        this.hapticFeedback('heavy');
    }

    stopQRScanner() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
        }
        
        const modal = document.getElementById('qr-scanner-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        }
    }

    // ===================================
    // Advanced Gestures
    // ===================================

    setupAdvancedGestures() {
        let touchStartTime = 0;
        let touchCount = 0;
        let lastTouchEnd = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchCount = e.touches.length;
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - touchStartTime;
            
            // Double tap detection
            if (touchDuration < 300 && touchCount === 1) {
                const timeSinceLastTouch = touchEndTime - lastTouchEnd;
                if (timeSinceLastTouch < 300 && timeSinceLastTouch > 0) {
                    this.handleDoubleTap(e);
                }
                lastTouchEnd = touchEndTime;
            }
            
            // Long press detection
            if (touchDuration > 800 && touchCount === 1) {
                this.handleLongPress(e);
            }
        }, { passive: true });
    }

    handleDoubleTap(e) {
        const target = e.target.closest('.product-card-mobile');
        if (target) {
            // Double tap on product card to add to wishlist
            this.toggleWishlist(target);
            this.hapticFeedback('light');
        }
    }

    handleLongPress(e) {
        const target = e.target.closest('.product-card-mobile');
        if (target) {
            // Long press to show quick actions
            this.showQuickActions(target, e);
            this.hapticFeedback('medium');
        }
    }

    // ===================================
    // Share API
    // ===================================

    setupShareAPI() {
        // Add share buttons to products
        document.querySelectorAll('.product-card-mobile').forEach(card => {
            this.addShareButton(card);
        });
    }

    addShareButton(productCard) {
        const shareBtn = document.createElement('button');
        shareBtn.className = 'btn-share-mobile';
        shareBtn.innerHTML = '📤';
        shareBtn.onclick = (e) => {
            e.preventDefault();
            this.shareProduct(productCard);
        };

        const actionsContainer = productCard.querySelector('.product-actions-mobile');
        if (actionsContainer) {
            actionsContainer.appendChild(shareBtn);
        }
    }

    async shareProduct(productCard) {
        const productTitle = productCard.querySelector('.product-title-mobile')?.textContent || 'منتج';
        const productUrl = window.location.href;
        const productImage = productCard.querySelector('img')?.src;

        const shareData = {
            title: `${productTitle} - دليل قطع الغيار`,
            text: `اكتشف هذا المنتج الرائع في دليل قطع الغيار`,
            url: productUrl
        };

        try {
            if (navigator.share && navigator.canShare(shareData)) {
                await navigator.share(shareData);
                this.showToast('تم مشاركة المنتج بنجاح!', 'success');
            } else {
                // Fallback to clipboard
                await navigator.clipboard.writeText(productUrl);
                this.showToast('تم نسخ رابط المنتج!', 'success');
            }
            this.hapticFeedback('light');
        } catch (error) {
            console.error('Error sharing:', error);
            this.showToast('حدث خطأ في المشاركة', 'error');
        }
    }

    // ===================================
    // Haptic Feedback
    // ===================================

    setupHapticFeedback() {
        // Enable haptic feedback for buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn, a[role="button"]')) {
                this.hapticFeedback('light');
            }
        });
    }

    hapticFeedback(intensity = 'light') {
        if ('vibrate' in navigator) {
            const patterns = {
                light: [10],
                medium: [20],
                heavy: [30, 10, 30]
            };
            navigator.vibrate(patterns[intensity] || patterns.light);
        }
    }

    // ===================================
    // Utility Methods
    // ===================================

    performSearch(query) {
        // Implement search functionality
        const searchUrl = `/search?q=${encodeURIComponent(query)}`;
        window.location.href = searchUrl;
    }

    toggleWishlist(productCard) {
        const wishlistBtn = productCard.querySelector('.btn-wishlist-mobile');
        if (wishlistBtn) {
            wishlistBtn.classList.toggle('active');
            const isActive = wishlistBtn.classList.contains('active');
            this.showToast(isActive ? 'تم إضافة المنتج للمفضلة' : 'تم إزالة المنتج من المفضلة', 'success');
        }
    }

    showQuickActions(productCard, event) {
        const quickActions = document.createElement('div');
        quickActions.className = 'quick-actions-modal';
        quickActions.innerHTML = `
            <div class="quick-actions-content">
                <button onclick="this.closest('.quick-actions-modal').remove()">إضافة للسلة</button>
                <button onclick="this.closest('.quick-actions-modal').remove()">إضافة للمفضلة</button>
                <button onclick="this.closest('.quick-actions-modal').remove()">مشاركة</button>
                <button onclick="this.closest('.quick-actions-modal').remove()">إلغاء</button>
            </div>
        `;

        document.body.appendChild(quickActions);
        setTimeout(() => quickActions.classList.add('show'), 100);

        // Remove after 5 seconds
        setTimeout(() => {
            if (quickActions.parentNode) {
                quickActions.remove();
            }
        }, 5000);
    }

    showToast(message, type = 'info') {
        // Use the toast method from the main PWA class
        if (window.dalilPWA) {
            window.dalilPWA.showToast(message, type);
        } else {
            console.log(`Toast: ${message} (${type})`);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.advancedFeatures = new AdvancedMobileFeatures();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedMobileFeatures;
}
