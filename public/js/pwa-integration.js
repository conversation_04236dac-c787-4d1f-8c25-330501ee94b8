/**
 * PWA Integration with Existing Dalil Auto Parts System
 * Connects PWA features with the current Laravel/Botble system
 */

class PWAIntegration {
    constructor() {
        this.apiBase = '/api/v1';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        this.currentUser = null;
        this.cartCount = 0;
        this.wishlistCount = 0;
        
        this.init();
    }

    init() {
        this.setupCSRFToken();
        this.integrateWithExistingCart();
        this.integrateWithWishlist();
        this.integrateWithAuth();
        this.enhanceExistingElements();
        this.setupOfflineSupport();
        this.initializeNotifications();
    }

    setupCSRFToken() {
        // Add CSRF token to all AJAX requests
        if (window.jQuery) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken
                }
            });
        }

        // Add to fetch requests
        const originalFetch = window.fetch;
        window.fetch = (url, options = {}) => {
            if (options.method && options.method.toUpperCase() !== 'GET') {
                options.headers = {
                    'X-CSRF-TOKEN': this.csrfToken,
                    ...options.headers
                };
            }
            return originalFetch(url, options);
        };
    }

    integrateWithExistingCart() {
        // Enhance existing cart functionality
        this.observeCartChanges();
        this.enhanceCartButtons();
        this.setupCartSync();
    }

    observeCartChanges() {
        // Watch for cart count changes
        const cartCountElements = document.querySelectorAll('[data-bb-value="cart-count"]');
        
        if (cartCountElements.length > 0) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' || mutation.type === 'characterData') {
                        const newCount = parseInt(mutation.target.textContent) || 0;
                        if (newCount !== this.cartCount) {
                            this.cartCount = newCount;
                            this.updateMobileCartBadge(newCount);
                            this.syncCartOffline();
                        }
                    }
                });
            });

            cartCountElements.forEach(element => {
                observer.observe(element, {
                    childList: true,
                    characterData: true,
                    subtree: true
                });
            });
        }
    }

    enhanceCartButtons() {
        // Add mobile-friendly enhancements to cart buttons
        document.querySelectorAll('.add-to-cart-button, [data-bb-toggle="add-to-cart"]').forEach(button => {
            button.classList.add('btn-mobile-enhanced');
            
            // Add loading state
            const originalClick = button.onclick;
            button.onclick = (e) => {
                button.classList.add('btn-loading');
                button.disabled = true;
                
                // Restore after 2 seconds if no response
                setTimeout(() => {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                }, 2000);
                
                if (originalClick) {
                    originalClick.call(button, e);
                }
            };
        });
    }

    setupCartSync() {
        // Sync cart data for offline use
        document.addEventListener('ecommerce.cart.added', (e) => {
            this.syncCartOffline();
            this.showToast('تم إضافة المنتج للسلة', 'success');
            this.hapticFeedback('light');
        });

        document.addEventListener('ecommerce.cart.removed', (e) => {
            this.syncCartOffline();
            this.showToast('تم إزالة المنتج من السلة', 'info');
        });
    }

    integrateWithWishlist() {
        // Enhance wishlist functionality
        document.querySelectorAll('.add-to-wishlist, [data-bb-toggle="add-to-wishlist"]').forEach(button => {
            button.classList.add('btn-mobile-enhanced');
            
            button.addEventListener('click', () => {
                this.hapticFeedback('light');
                setTimeout(() => {
                    this.syncWishlistOffline();
                }, 500);
            });
        });

        // Watch for wishlist changes
        document.addEventListener('ecommerce.wishlist.added', () => {
            this.showToast('تم إضافة المنتج للمفضلة', 'success');
            this.syncWishlistOffline();
        });

        document.addEventListener('ecommerce.wishlist.removed', () => {
            this.showToast('تم إزالة المنتج من المفضلة', 'info');
            this.syncWishlistOffline();
        });
    }

    integrateWithAuth() {
        // Check if user is logged in
        this.checkAuthStatus();
        
        // Enhance login/register forms
        this.enhanceAuthForms();
        
        // Setup OTP integration
        this.setupOTPIntegration();
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/api/v1/auth/user');
            if (response.ok) {
                this.currentUser = await response.json();
                this.updateUIForLoggedInUser();
            }
        } catch (error) {
            console.log('User not logged in');
        }
    }

    enhanceAuthForms() {
        // Add mobile enhancements to auth forms
        document.querySelectorAll('form[action*="login"], form[action*="register"]').forEach(form => {
            form.classList.add('mobile-enhanced-form');
            
            // Add loading states
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.classList.add('btn-loading');
                    submitBtn.disabled = true;
                }
            });
        });
    }

    setupOTPIntegration() {
        // Enhance OTP input fields
        document.querySelectorAll('input[name*="otp"], input[name*="verification"]').forEach(input => {
            input.type = 'tel';
            input.inputMode = 'numeric';
            input.pattern = '[0-9]*';
            input.maxLength = 6;
            
            // Auto-focus next input
            input.addEventListener('input', (e) => {
                if (e.target.value.length === 1) {
                    const nextInput = e.target.nextElementSibling;
                    if (nextInput && nextInput.tagName === 'INPUT') {
                        nextInput.focus();
                    }
                }
            });
        });
    }

    enhanceExistingElements() {
        // Enhance product cards
        this.enhanceProductCards();
        
        // Enhance navigation
        this.enhanceNavigation();
        
        // Enhance search
        this.enhanceSearch();
        
        // Add mobile-specific classes
        this.addMobileClasses();
    }

    enhanceProductCards() {
        document.querySelectorAll('.product-item, .product-card').forEach(card => {
            card.classList.add('product-card-mobile');
            
            // Add touch feedback
            card.addEventListener('touchstart', () => {
                card.style.transform = 'scale(0.98)';
            });
            
            card.addEventListener('touchend', () => {
                card.style.transform = 'scale(1)';
            });
            
            // Add quick actions on long press
            let longPressTimer;
            card.addEventListener('touchstart', (e) => {
                longPressTimer = setTimeout(() => {
                    this.showQuickActions(card, e);
                    this.hapticFeedback('medium');
                }, 800);
            });
            
            card.addEventListener('touchend', () => {
                clearTimeout(longPressTimer);
            });
        });
    }

    enhanceNavigation() {
        // Add mobile navigation enhancements
        const mobileMenu = document.querySelector('.tp-mobile-menu');
        if (mobileMenu) {
            mobileMenu.classList.add('mobile-nav-enhanced');
        }

        // Add swipe gestures to navigation
        this.setupNavigationGestures();
    }

    enhanceSearch() {
        // Enhance search inputs
        document.querySelectorAll('input[type="search"], input[name*="search"]').forEach(input => {
            const container = input.closest('.search-bar, .search-form') || input.parentElement;
            container.classList.add('search-bar-mobile');
            input.classList.add('search-input-mobile');
            
            // Add voice search if supported
            if (window.advancedFeatures && window.advancedFeatures.isVoiceSearchSupported) {
                window.advancedFeatures.addVoiceSearchButton(container);
            }
        });
    }

    addMobileClasses() {
        // Add mobile-specific classes based on screen size
        if (window.innerWidth <= 768) {
            document.body.classList.add('mobile-device');
            
            // Add safe area classes for iOS
            if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                document.body.classList.add('ios-device');
            }
        }
    }

    setupOfflineSupport() {
        // Setup offline data sync
        this.setupOfflineSync();
        
        // Handle online/offline events
        window.addEventListener('online', () => {
            this.syncAllOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.showToast('وضع عدم الاتصال - سيتم حفظ البيانات محلياً', 'warning');
        });
    }

    setupOfflineSync() {
        // Sync cart data
        setInterval(() => {
            if (navigator.onLine) {
                this.syncCartOffline();
                this.syncWishlistOffline();
            }
        }, 30000); // Every 30 seconds
    }

    async syncCartOffline() {
        try {
            const cartData = await this.getCurrentCartData();
            localStorage.setItem('offline_cart', JSON.stringify(cartData));
        } catch (error) {
            console.error('Error syncing cart offline:', error);
        }
    }

    async syncWishlistOffline() {
        try {
            const wishlistData = await this.getCurrentWishlistData();
            localStorage.setItem('offline_wishlist', JSON.stringify(wishlistData));
        } catch (error) {
            console.error('Error syncing wishlist offline:', error);
        }
    }

    async getCurrentCartData() {
        try {
            let cartId = localStorage.getItem('cart_id') || 'guest_' + Date.now();
            localStorage.setItem('cart_id', cartId);

            const response = await fetch(`/api/v1/ecommerce/cart/${cartId}`);
            if (!response.ok) {
                return { data: { items: [], count: 0 } };
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching cart data:', error);
            return { data: { items: [], count: 0 } };
        }
    }

    async getCurrentWishlistData() {
        try {
            let wishlistId = localStorage.getItem('wishlist_id');
            if (!wishlistId) {
                wishlistId = 'wishlist_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
                localStorage.setItem('wishlist_id', wishlistId);
            }

            const response = await fetch(`/api/v1/ecommerce/wishlist/${wishlistId}`);
            if (!response.ok) {
                return { data: { items: [] } };
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching wishlist data:', error);
            return { data: { items: [] } };
        }
    }

    async syncAllOfflineData() {
        try {
            // Sync cart
            const offlineCart = localStorage.getItem('offline_cart');
            if (offlineCart) {
                await this.syncCartToServer(JSON.parse(offlineCart));
                localStorage.removeItem('offline_cart');
            }

            // Sync wishlist
            const offlineWishlist = localStorage.getItem('offline_wishlist');
            if (offlineWishlist) {
                await this.syncWishlistToServer(JSON.parse(offlineWishlist));
                localStorage.removeItem('offline_wishlist');
            }

            this.showToast('تم مزامنة البيانات بنجاح', 'success');
        } catch (error) {
            console.error('Error syncing offline data:', error);
        }
    }

    async syncCartToServer(cartData) {
        const response = await fetch('/api/v1/cart/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.csrfToken
            },
            body: JSON.stringify(cartData)
        });
        return response.json();
    }

    async syncWishlistToServer(wishlistData) {
        const response = await fetch('/api/v1/wishlist/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.csrfToken
            },
            body: JSON.stringify(wishlistData)
        });
        return response.json();
    }

    initializeNotifications() {
        // Setup push notifications
        if ('Notification' in window && 'serviceWorker' in navigator) {
            this.setupPushNotifications();
        }
    }

    async setupPushNotifications() {
        try {
            const registration = await navigator.serviceWorker.ready;
            
            // Check if already subscribed
            const existingSubscription = await registration.pushManager.getSubscription();
            if (existingSubscription) {
                console.log('Already subscribed to push notifications');
                return;
            }

            // Request permission
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                // Subscribe to push notifications
                const subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: this.urlBase64ToUint8Array(window.vapidPublicKey || '')
                });

                // Send subscription to server
                await this.sendSubscriptionToServer(subscription);
            }
        } catch (error) {
            console.error('Error setting up push notifications:', error);
        }
    }

    async sendSubscriptionToServer(subscription) {
        const response = await fetch('/api/v1/notifications/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.csrfToken
            },
            body: JSON.stringify(subscription)
        });
        return response.json();
    }

    // Utility methods
    updateMobileCartBadge(count) {
        const badges = document.querySelectorAll('.mobile-nav-badge, .cart-badge');
        badges.forEach(badge => {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
        });
    }

    updateUIForLoggedInUser() {
        // Update UI elements for logged in user
        document.body.classList.add('user-logged-in');
        
        // Show user-specific elements
        document.querySelectorAll('.auth-required').forEach(el => {
            el.style.display = 'block';
        });
        
        // Hide login prompts
        document.querySelectorAll('.login-prompt').forEach(el => {
            el.style.display = 'none';
        });
    }

    setupNavigationGestures() {
        let startX = 0;
        let startY = 0;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // Swipe right to open menu
            if (deltaX > 100 && Math.abs(deltaY) < 50 && startX < 50) {
                const menuToggle = document.querySelector('.offcanvas-toggle, .mobile-menu-toggle');
                if (menuToggle) {
                    menuToggle.click();
                }
            }
        }, { passive: true });
    }

    showQuickActions(element, event) {
        // Implementation would show quick action menu
        console.log('Show quick actions for element:', element);
    }

    showToast(message, type = 'info') {
        if (window.dalilPWA) {
            window.dalilPWA.showToast(message, type);
        }
    }

    hapticFeedback(intensity = 'light') {
        if (window.advancedFeatures) {
            window.advancedFeatures.hapticFeedback(intensity);
        }
    }

    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }
}

// Initialize PWA Integration when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.pwaIntegration = new PWAIntegration();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAIntegration;
}
