/**
 * Mobile PWA Features for Dalil Auto Parts
 * Advanced mobile functionality and PWA capabilities
 */

class DalilMobilePWA {
    constructor() {
        this.isOnline = navigator.onLine;
        this.installPrompt = null;
        this.notificationPermission = 'default';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkPWAInstallability();
        this.setupOfflineSync();
        this.initializeNotifications();
        this.setupTouchGestures();
        this.optimizeForMobile();
    }

    setupEventListeners() {
        // Online/Offline status
        window.addEventListener('online', () => this.handleOnlineStatus(true));
        window.addEventListener('offline', () => this.handleOnlineStatus(false));

        // PWA install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.installPrompt = e;
            this.showInstallButton();
        });

        // App installed
        window.addEventListener('appinstalled', () => {
            console.log('PWA installed successfully');
            this.hideInstallButton();
            this.trackEvent('pwa_installed');
        });

        // Visibility change for background sync
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isOnline) {
                this.syncOfflineData();
            }
        });
    }

    handleOnlineStatus(online) {
        this.isOnline = online;
        const statusElement = document.getElementById('connection-status');
        
        if (statusElement) {
            statusElement.textContent = online ? '🟢 متصل' : '🔴 غير متصل';
            statusElement.className = online ? 'online' : 'offline';
        }

        if (online) {
            this.syncOfflineData();
            this.showToast('تم استعادة الاتصال بالإنترنت', 'success');
        } else {
            this.showToast('انقطع الاتصال بالإنترنت - سيتم حفظ البيانات محلياً', 'warning');
        }
    }

    checkPWAInstallability() {
        // Check if app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches) {
            console.log('App is running in standalone mode');
            this.hideInstallButton();
            return;
        }

        // Check if running in PWA context
        if (window.navigator.standalone === true) {
            console.log('App is running in iOS standalone mode');
            this.hideInstallButton();
            return;
        }

        // Show install prompt for supported browsers
        setTimeout(() => {
            if (this.installPrompt) {
                this.showInstallButton();
            }
        }, 3000);
    }

    showInstallButton() {
        const installButton = document.createElement('button');
        installButton.id = 'pwa-install-btn';
        installButton.className = 'btn-mobile-enhanced pwa-install-btn';
        installButton.innerHTML = '📱 تثبيت التطبيق';
        installButton.onclick = () => this.installPWA();

        // Add to header or create floating button
        const header = document.querySelector('header') || document.body;
        header.appendChild(installButton);

        // Show with animation
        setTimeout(() => {
            installButton.style.opacity = '1';
            installButton.style.transform = 'translateY(0)';
        }, 100);
    }

    hideInstallButton() {
        const installButton = document.getElementById('pwa-install-btn');
        if (installButton) {
            installButton.remove();
        }
    }

    async installPWA() {
        if (!this.installPrompt) return;

        try {
            const result = await this.installPrompt.prompt();
            console.log('Install prompt result:', result.outcome);
            
            if (result.outcome === 'accepted') {
                this.trackEvent('pwa_install_accepted');
            } else {
                this.trackEvent('pwa_install_dismissed');
            }
            
            this.installPrompt = null;
            this.hideInstallButton();
        } catch (error) {
            console.error('Error installing PWA:', error);
        }
    }

    setupOfflineSync() {
        // Register for background sync if supported
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            navigator.serviceWorker.ready.then(registration => {
                // Register sync events
                this.registerBackgroundSync(registration);
            });
        }
    }

    registerBackgroundSync(registration) {
        // Cart sync
        document.addEventListener('cart-updated', () => {
            if (!this.isOnline) {
                registration.sync.register('cart-sync');
            }
        });

        // Order sync
        document.addEventListener('order-created', () => {
            if (!this.isOnline) {
                registration.sync.register('order-sync');
            }
        });
    }

    async syncOfflineData() {
        try {
            // Sync cart data
            const cartData = this.getOfflineData('cart');
            if (cartData) {
                await this.syncCart(cartData);
                this.clearOfflineData('cart');
            }

            // Sync wishlist
            const wishlistData = this.getOfflineData('wishlist');
            if (wishlistData) {
                await this.syncWishlist(wishlistData);
                this.clearOfflineData('wishlist');
            }

            // Sync search history
            const searchData = this.getOfflineData('searches');
            if (searchData) {
                await this.syncSearchHistory(searchData);
                this.clearOfflineData('searches');
            }

        } catch (error) {
            console.error('Error syncing offline data:', error);
        }
    }

    async initializeNotifications() {
        if ('Notification' in window) {
            this.notificationPermission = Notification.permission;
            
            if (this.notificationPermission === 'default') {
                // Show permission request after user interaction
                setTimeout(() => this.requestNotificationPermission(), 5000);
            }
        }
    }

    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;
            
            if (permission === 'granted') {
                this.showToast('تم تفعيل الإشعارات بنجاح!', 'success');
                this.subscribeToNotifications();
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
        }
    }

    async subscribeToNotifications() {
        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(window.vapidPublicKey || '')
            });

            // Send subscription to server
            await fetch('/api/v1/notifications/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify(subscription)
            });

            console.log('Push notification subscription successful');
        } catch (error) {
            console.error('Error subscribing to notifications:', error);
        }
    }

    setupTouchGestures() {
        let startX, startY, startTime;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // Swipe detection
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    this.handleSwipeRight();
                } else {
                    this.handleSwipeLeft();
                }
            }

            // Pull to refresh
            if (deltaY > 100 && window.scrollY === 0 && deltaTime < 500) {
                this.handlePullToRefresh();
            }
        }, { passive: true });
    }

    handleSwipeRight() {
        // Open navigation menu or go back
        const navToggle = document.querySelector('.mobile-nav-toggle');
        if (navToggle) {
            navToggle.click();
        }
    }

    handleSwipeLeft() {
        // Close navigation menu or open cart
        const cartToggle = document.querySelector('.cart-toggle');
        if (cartToggle) {
            cartToggle.click();
        }
    }

    handlePullToRefresh() {
        if (this.isOnline) {
            this.showToast('جاري تحديث البيانات...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }
    }

    optimizeForMobile() {
        // Optimize images for mobile
        this.lazyLoadImages();
        
        // Optimize forms
        this.enhanceMobileForms();
        
        // Add mobile-specific classes
        document.body.classList.add('mobile-optimized');
        
        // Handle viewport changes
        this.handleViewportChanges();
    }

    lazyLoadImages() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    enhanceMobileForms() {
        // Add appropriate input types for mobile
        document.querySelectorAll('input[type="text"]').forEach(input => {
            if (input.name.includes('email')) {
                input.type = 'email';
            } else if (input.name.includes('phone') || input.name.includes('tel')) {
                input.type = 'tel';
            } else if (input.name.includes('number') || input.name.includes('qty')) {
                input.type = 'number';
            }
        });

        // Add mobile-friendly attributes
        document.querySelectorAll('input, textarea').forEach(field => {
            field.setAttribute('autocomplete', 'on');
            field.setAttribute('autocapitalize', 'sentences');
        });
    }

    handleViewportChanges() {
        let viewportHeight = window.innerHeight;
        
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = viewportHeight - currentHeight;
            
            // Detect virtual keyboard
            if (heightDifference > 150) {
                document.body.classList.add('keyboard-open');
            } else {
                document.body.classList.remove('keyboard-open');
            }
            
            viewportHeight = currentHeight;
        });
    }

    // Utility methods
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    getOfflineData(key) {
        try {
            return JSON.parse(localStorage.getItem(`offline_${key}`));
        } catch {
            return null;
        }
    }

    setOfflineData(key, data) {
        try {
            localStorage.setItem(`offline_${key}`, JSON.stringify(data));
        } catch (error) {
            console.error('Error saving offline data:', error);
        }
    }

    clearOfflineData(key) {
        localStorage.removeItem(`offline_${key}`);
    }

    trackEvent(eventName, data = {}) {
        // Analytics tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, data);
        }
        console.log('Event tracked:', eventName, data);
    }

    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    // API methods for syncing
    async syncCart(cartData) {
        const response = await fetch('/api/v1/cart/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify(cartData)
        });
        return response.json();
    }

    async syncWishlist(wishlistData) {
        const response = await fetch('/api/v1/wishlist/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify(wishlistData)
        });
        return response.json();
    }

    async syncSearchHistory(searchData) {
        const response = await fetch('/api/v1/search/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
            },
            body: JSON.stringify(searchData)
        });
        return response.json();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.dalilPWA = new DalilMobilePWA();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DalilMobilePWA;
}
