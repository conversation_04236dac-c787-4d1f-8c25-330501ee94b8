<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>دليل قطع الغيار - تطبيق محمول</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليل قطع الغيار">
    <meta name="msapplication-TileColor" content="#0C55AA">
    
    <!-- Manifest -->
    <link rel="manifest" href="/pwa/manifest.json">
    
    <!-- Icons -->
    <link rel="apple-touch-icon" sizes="192x192" href="/pwa/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/pwa/icon-32x32.png">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="dummy-token-for-demo">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/mobile-pwa-enhancements.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding-bottom: 80px; /* Space for bottom nav */
            overflow-x: hidden;
        }
        
        /* Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            padding: 10px 16px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(12, 85, 170, 0.3);
            padding-top: calc(10px + env(safe-area-inset-top));
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .logo-img {
            height: 45px;
            width: auto;
            max-width: 180px;
            border-radius: 8px;
            background: white;
            padding: 6px 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-img img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .header-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }

        /* User Menu Dropdown */
        .user-menu-dropdown {
            position: fixed;
            top: 70px;
            right: 16px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            min-width: 280px;
            max-width: 320px;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .user-menu-content {
            padding: 20px;
        }

        .user-menu-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .user-status {
            font-size: 12px;
            color: #666;
        }

        .user-menu-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .user-menu-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .user-menu-btn.primary {
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
        }

        .user-menu-btn.secondary {
            background: #f8f9fa;
            color: #0C55AA;
            border: 2px solid #0C55AA;
        }

        .user-menu-btn.danger {
            background: #dc3545;
            color: white;
        }

        .user-menu-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .user-menu-links {
            margin-bottom: 15px;
        }

        .user-menu-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            color: #333;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .user-menu-link:hover {
            color: #0C55AA;
            padding-right: 8px;
        }

        .user-menu-link:last-child {
            border-bottom: none;
        }

        .link-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }
        
        .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 0 16px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Search Bar */
        .search-section {
            margin: 20px 0;
        }
        
        .search-container {
            position: relative;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .search-input {
            width: 100%;
            padding: 16px 50px 16px 20px;
            border: none;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            outline: none;
            background: transparent;
        }
        
        .search-input::placeholder {
            color: #999;
        }
        
        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #0C55AA;
            color: white;
            border: none;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: #094488;
        }
        
        /* Vehicle Parts Finder */
        .vpf-section {
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            border-radius: 20px;
            padding: 24px;
            margin: 20px 0;
            color: white;
            box-shadow: 0 8px 25px rgba(12, 85, 170, 0.3);
        }
        
        .vpf-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .vpf-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 28px;
        }
        
        .vpf-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .vpf-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .vpf-form {
            display: grid;
            gap: 12px;
        }
        
        .vpf-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 14px 16px;
            color: white;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .vpf-select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .vpf-select option {
            background: #0C55AA;
            color: white;
        }
        
        .vpf-btn {
            background: white;
            color: #0C55AA;
            border: none;
            padding: 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 8px;
        }
        
        .vpf-btn:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
        }
        
        /* Quick Actions */
        .quick-actions {
            margin: 20px 0;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .quick-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        
        .quick-item {
            background: white;
            border-radius: 15px;
            padding: 16px 8px;
            text-align: center;
            text-decoration: none;
            color: #333;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .quick-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: #0C55AA;
        }
        
        .quick-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 24px;
        }
        
        .quick-text {
            font-size: 12px;
            font-weight: 600;
            line-height: 1.3;
        }
        
        /* Categories */
        .categories-section {
            margin: 20px 0;
        }
        
        .categories-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 4px 0 8px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .categories-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-item {
            min-width: 120px;
            background: white;
            border-radius: 15px;
            padding: 16px 12px;
            text-align: center;
            text-decoration: none;
            color: #333;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        }
        
        .category-img {
            width: 60px;
            height: 60px;
            background: #f8f9fa;
            border-radius: 12px;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
        }
        
        .category-name {
            font-size: 13px;
            font-weight: 600;
            line-height: 1.3;
        }
        
        /* Featured Products */
        .products-section {
            margin: 20px 0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            margin-bottom: 16px;
        }

        .scroll-hint {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #666;
            opacity: 0.8;
        }

        .scroll-arrow {
            animation: slideLeft 2s infinite;
            font-size: 14px;
        }

        @keyframes slideLeft {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-4px); }
        }

        /* إخفاء مؤشر التمرير بعد التفاعل */
        .products-grid.scrolled + .section-header .scroll-hint,
        .flash-sale-grid.scrolled + .section-header .scroll-hint {
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .products-grid {
            display: flex;
            overflow-x: auto;
            gap: 16px;
            padding: 0 16px 16px 16px;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        .products-grid::-webkit-scrollbar {
            height: 4px;
        }

        .products-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .products-grid::-webkit-scrollbar-thumb {
            background: #0C55AA;
            border-radius: 2px;
        }

        .products-grid::-webkit-scrollbar-thumb:hover {
            background: #094a96;
        }
        
        .product-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            color: #333;
            min-width: 280px;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.06);
            position: relative;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(12, 85, 170, 0.2);
        }

        .product-card:active {
            transform: translateY(-2px) scale(1.01);
            transition: all 0.1s ease;
        }
        
        .product-img {
            width: 100%;
            height: 160px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-card:hover .product-img img {
            transform: scale(1.08);
        }
        
        .product-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4);
            z-index: 2;
            backdrop-filter: blur(10px);
        }

        .product-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            border-radius: 20px;
        }
        
        .product-info {
            padding: 16px;
            background: white;
        }

        .product-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            color: #1a1a1a;
            min-height: 42px;
        }

        .product-name:hover {
            color: #0C55AA;
            transition: color 0.2s ease;
        }
        
        .product-price {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 16px;
        }

        .price-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .price-current {
            font-size: 16px;
            font-weight: 700;
            color: #0C55AA;
        }
        
        .price-old {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
        }
        
        .product-actions {
            display: flex;
            gap: 10px;
            margin-top: auto;
        }

        .btn-cart, .add-to-cart-btn {
            flex: 1;
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(12, 85, 170, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-cart::before, .add-to-cart-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-cart:hover::before, .add-to-cart-btn:hover::before {
            left: 100%;
        }

        .btn-cart:hover, .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #094488 0%, #1a3460 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(12, 85, 170, 0.4);
        }

        .btn-cart:active, .add-to-cart-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(12, 85, 170, 0.3);
        }
        
        .btn-wishlist, .wishlist-btn {
            width: 44px;
            height: 44px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-wishlist:hover, .wishlist-btn:hover {
            background: #fff5f5;
            border-color: #ff6b6b;
            color: #ff6b6b;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .btn-wishlist.active, .wishlist-btn.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border-color: #ff6b6b;
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            text-decoration: none;
            color: #6c757d;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 60px;
            position: relative;
        }
        
        .nav-item:hover,
        .nav-item.active {
            color: #0C55AA;
            background: rgba(12, 85, 170, 0.1);
        }
        
        .nav-icon {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-icon svg {
            width: 22px;
            height: 22px;
            stroke-width: 2;
            transition: all 0.3s ease;
        }
        
        .nav-text {
            font-size: 11px;
            font-weight: 600;
        }
        
        .nav-badge {
            position: absolute;
            top: 2px;
            right: 8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        /* Loading States */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0C55AA;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 480px) {
            .main-content {
                padding: 0 12px;
            }
            
            .quick-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;
            }
            
            .products-grid {
                gap: 8px;
            }
            
            .product-info {
                padding: 10px;
            }
        }
        
        /* Animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-up {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }

        /* Slider Styles */
        .slider-item {
            min-width: 100%;
            height: 200px;
            position: relative;
            display: flex;
            align-items: center;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-color: #0C55AA; /* Fallback color */
        }

        .slider-content {
            padding: 24px;
            color: white;
            z-index: 2;
            position: relative;
            max-width: 70%;
        }

        .slider-content.light {
            color: #333;
        }

        .slider-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.1) 100%);
            z-index: 1;
        }

        .slider-content.light + .slider-overlay {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
        }

        .slider-subtitle {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 6px;
            opacity: 0.9;
        }

        .slider-title {
            font-size: 18px;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 8px;
        }

        .slider-description {
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .slider-btn {
            background: #0C55AA;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .slider-btn:hover {
            background: #094488;
            transform: translateY(-1px);
        }

        .slider-content.light .slider-btn {
            background: #333;
            color: white;
        }

        .slider-nav:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.1);
        }

        .slider-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-dot.active {
            background: white;
            transform: scale(1.2);
        }

        /* Wishlist Button Styles */
        .btn-wishlist {
            position: relative;
            overflow: hidden;
        }

        .btn-wishlist::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(220, 53, 69, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .btn-wishlist:hover::before,
        .btn-wishlist.active::before {
            width: 100px;
            height: 100px;
        }

        /* Flash Sale Styles */
        .flash-sale-section {
            margin: 20px 0;
        }

        .flash-sale-grid {
            display: flex;
            overflow-x: auto;
            gap: 16px;
            padding: 0 16px 16px 16px;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        .flash-sale-grid::-webkit-scrollbar {
            height: 4px;
        }

        .flash-sale-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .flash-sale-grid::-webkit-scrollbar-thumb {
            background: #ff4757;
            border-radius: 2px;
        }

        .flash-sale-item {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            color: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 250px;
            flex-shrink: 0;
            position: relative;
        }

        .flash-sale-image {
            position: relative;
            height: 120px;
            overflow: hidden;
        }

        .flash-sale-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .flash-sale-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.95);
            color: #ff4757;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 700;
        }

        .flash-sale-info {
            padding: 12px;
        }

        .flash-sale-name {
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .flash-sale-price {
            margin-bottom: 10px;
        }

        .flash-sale-price .current-price {
            font-size: 14px;
            font-weight: 700;
        }

        .flash-sale-price .original-price {
            font-size: 11px;
            text-decoration: line-through;
            opacity: 0.8;
            margin-right: 4px;
        }

        .flash-sale-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .flash-sale-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .flash-sale-item:hover {
            transform: translateY(-2px);
        }

        .sale-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        /* All Products Styles */
        .all-products-section {
            margin: 20px 0;
        }

        .section-header-full {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            margin-bottom: 16px;
        }

        .products-info {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 12px;
        }

        /* Products Grid - 2x2 for mobile */
        .products-grid-full {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            padding: 0 16px;
            margin-bottom: 20px;
        }

        .view-more-container {
            text-align: center;
            margin: 20px 0;
            padding: 0 16px;
        }

        .view-more-btn {
            background: linear-gradient(135deg, #0C55AA, #1e3c72);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 auto 10px auto;
        }

        .view-more-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(12, 85, 170, 0.3);
        }

        .pagination-info {
            font-size: 12px;
            color: #666;
            margin-top: 8px;
        }

        /* Blog Styles */
        .blog-section {
            margin: 20px 0;
        }

        .blog-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 0 16px;
        }

        .blog-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .blog-card:hover {
            transform: translateY(-2px);
        }

        .blog-image {
            width: 100%;
            height: 120px;
            overflow: hidden;
        }

        .blog-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .blog-content {
            padding: 12px;
        }

        .blog-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
            color: #333;
        }

        .blog-excerpt {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .blog-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .blog-date {
            font-size: 11px;
            color: #999;
        }

        /* Enhanced Price Styles */
        .price-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .price-label {
            font-size: 11px;
            color: #666;
            min-width: 40px;
        }

        .current-price {
            font-weight: 600;
            color: #0C55AA;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 12px;
        }

        .wholesale-price {
            font-weight: 600;
            color: #28a745;
        }

        .price-row.wholesale {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 6px;
            margin-top: 4px;
        }

        .price-on-request {
            color: #666;
            font-style: italic;
            font-size: 12px;
        }

        .wholesale-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 8px;
        }

        .admin-badge {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 8px;
        }

        .wholesale-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 8px;
            margin-top: 4px;
        }

        .wholesale-info-text {
            font-size: 11px;
            color: #1976d2;
            line-height: 1.3;
        }

        .wholesale-info-text a {
            color: #1976d2;
            font-weight: 600;
            text-decoration: underline;
        }

        .btn-wishlist.active {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
            animation: heartBeat 0.6s ease;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Loading skeleton for slider */
        .slider-skeleton {
            width: 100%;
            height: 200px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 20px;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Search Suggestions Styles */
        .search-suggestions .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .search-tag {
            background: #f0f0f0;
            color: #333;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .search-tag:hover {
            background: #0C55AA;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(12, 85, 170, 0.3);
        }

        /* Search Results Grid */
        #search-results-grid {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced Product Cards Responsive */
        @media (max-width: 480px) {
            .product-card {
                min-width: 180px;
            }

            .product-img {
                height: 140px;
            }

            .product-info {
                padding: 12px;
            }

            .product-name {
                font-size: 13px;
                margin-bottom: 8px;
                min-height: 36px;
            }

            .price-current {
                font-size: 15px;
            }

            .btn-cart, .add-to-cart-btn {
                padding: 10px 12px;
                font-size: 12px;
            }

            .btn-wishlist, .wishlist-btn {
                width: 38px;
                height: 38px;
                font-size: 16px;
            }
        }

        /* Large screens */
        @media (min-width: 768px) {
            .product-card {
                min-width: 320px;
            }

            .product-img {
                height: 180px;
            }

            .product-name {
                font-size: 16px;
                min-height: 48px;
            }

            .price-current {
                font-size: 18px;
            }
        }

        /* Products Modal Styles */
        .products-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            background: white;
            border-radius: 20px;
            width: 95%;
            max-width: 500px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideUp 0.3s ease-out;
        }

        @keyframes modalSlideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: linear-gradient(135deg, #0C55AA, #1e3c72);
            color: white;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: 20px;
            max-height: calc(90vh - 140px);
            overflow-y: auto;
        }

        .products-grid-modal {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        /* كروت المنتجات في الـ modal تستخدم نفس تصميم الكروت العادية */

        .loading-spinner {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 14px;
        }

        .error-message {
            text-align: center;
            padding: 40px;
            color: #dc3545;
            font-size: 14px;
        }

        /* Pagination Styles */
        .pagination-container {
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .pagination-info-modal {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 15px;
        }

        .pagination-buttons {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .page-btn {
            background: white;
            border: 1px solid #ddd;
            color: #333;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 35px;
        }

        .page-btn:hover {
            background: #f8f9fa;
            border-color: #0C55AA;
            color: #0C55AA;
        }

        .page-btn.active {
            background: #0C55AA;
            color: white;
            border-color: #0C55AA;
        }

        .page-dots {
            color: #666;
            font-size: 12px;
            padding: 0 4px;
        }

        /* Mobile responsive for modal */
        @media (max-width: 480px) {
            .modal-content {
                width: 98%;
                max-height: 95vh;
                border-radius: 15px;
            }

            .modal-header {
                padding: 15px;
            }

            .modal-body {
                padding: 15px;
                max-height: calc(95vh - 120px);
            }

            .products-grid-modal {
                gap: 8px;
            }

            /* كروت الـ modal تستخدم نفس responsive design للكروت العادية */

            .page-btn {
                padding: 6px 8px;
                font-size: 11px;
                min-width: 30px;
            }
        }

        /* الأسعار في الـ modal تستخدم نفس تصميم الكروت العادية */
    </style>
</head>
<body>
    <!-- Header -->
    <header class="mobile-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-img">
                    <img src="/storage/main/general/logo.png" alt="دليل قطع الغيار">
                </div>
            </div>
            <div class="header-actions">
                <button class="header-btn" id="notifications-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                        <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
                    </svg>
                    <span class="badge" id="notifications-count">3</span>
                </button>
                <button class="header-btn" id="menu-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="4" x2="20" y1="6" y2="6"/>
                        <line x1="4" x2="20" y1="12" y2="12"/>
                        <line x1="4" x2="20" y1="18" y2="18"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Search Section -->
        <section class="search-section fade-in">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="ابحث عن قطع الغيار..." id="main-search">
                <button class="search-btn" id="search-submit">
                    🔍
                </button>
            </div>
        </section>

        <!-- Main Slider Section -->
        <section class="slider-section fade-in" style="margin: 20px 0;">
            <div class="simple-slider-container" id="simple-slider-container">
                <!-- Simple slider will be loaded here -->
                <div class="slider-loading" style="
                    height: 200px;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: shimmer 1.5s infinite;
                    border-radius: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #666;
                    font-weight: 600;
                ">
                    جاري تحميل السلايدر...
                </div>
            </div>
        </section>

        <!-- Main Categories Section -->
        <section class="main-categories-section fade-in" style="margin: 20px 0;">
            <div class="main-categories-container" id="main-categories-container">
                <!-- Main categories will be loaded here -->
                <div class="categories-loading" style="
                    height: 120px;
                    background: #F3F5F7;
                    border-radius: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #666;
                    font-weight: 600;
                ">
                    جاري تحميل الفئات الرئيسية...
                </div>
            </div>
        </section>

        <!-- Vehicle Parts Finder -->
        <section class="vpf-section fade-in" id="vpf-section" style="
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            border-radius: 20px;
            padding: 24px;
            margin: 20px 0;
            color: white;
            box-shadow: 0 8px 25px rgba(12, 85, 170, 0.3);
        ">
            <!-- This will be loaded from the actual shortcode -->
            <div class="vpf-loading" style="text-align: center; padding: 20px;">
                <div class="vpf-loading-icon" style="
                    width: 60px;
                    height: 60px;
                    background: rgba(255, 255, 255, 0.15);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 16px;
                    font-size: 28px;
                ">🚗</div>
                <h3 style="font-size: 20px; font-weight: 700; margin-bottom: 8px;">البحث عن قطع الغيار</h3>
                <p style="font-size: 14px; opacity: 0.9;">جاري تحميل أداة البحث المتقدمة...</p>
            </div>
        </section>





        <!-- Featured Products -->
        <section class="products-section fade-in">
            <div class="section-header">
                <h3 class="section-title">
                    ⭐ المنتجات المميزة
                </h3>
                <div class="scroll-hint">
                    <span>اسحب للمزيد</span>
                    <span class="scroll-arrow">←</span>
                </div>
            </div>
            <div class="products-grid" id="featured-products">
                <!-- Products will be loaded here -->
            </div>
        </section>

        <!-- Flash Sale -->
        <section class="flash-sale-section fade-in" id="flash-sale-section" style="display: none;">
            <div class="section-header">
                <h3 class="section-title">
                    🔥 عروض اليوم
                </h3>
                <div class="scroll-hint">
                    <span>اسحب للمزيد</span>
                    <span class="scroll-arrow">←</span>
                </div>
            </div>
            <div id="flash-sale-container">
                <!-- Flash sale will be loaded here -->
            </div>
        </section>

        <!-- New Arrivals -->
        <section class="new-arrivals-section fade-in">
            <div class="section-header">
                <h3 class="section-title">
                    🆕 أحدث المنتجات
                </h3>
                <div class="scroll-hint">
                    <span>اسحب للمزيد</span>
                    <span class="scroll-arrow">←</span>
                </div>
            </div>
            <div class="products-grid" id="new-arrivals-container">
                <!-- New arrivals will be loaded here -->
            </div>
        </section>

        <!-- Best Selling Products -->
        <section class="best-selling-section fade-in">
            <div class="section-header">
                <h3 class="section-title">
                    🏆 أحسن المنتجات
                </h3>
                <div class="scroll-hint">
                    <span>اسحب للمزيد</span>
                    <span class="scroll-arrow">←</span>
                </div>
            </div>
            <div class="products-grid" id="best-selling-products">
                <!-- Best selling products will be loaded here -->
            </div>
        </section>

        <!-- All Products with Pagination -->
        <section class="all-products-section fade-in">
            <div class="section-header-full">
                <h3 class="section-title">
                    🛍️ جميع المنتجات
                </h3>
                <div class="products-info" id="products-info">
                    <span>جاري التحميل...</span>
                </div>
            </div>

            <!-- Products Grid (2x2 for mobile) -->
            <div class="products-grid-full" id="all-products-grid">
                <!-- Products will be loaded here -->
            </div>

            <!-- View More Button -->
            <div class="view-more-container" id="view-more-container" style="display: none;">
                <button class="view-more-btn" id="view-more-btn" onclick="app.showAllProductsPage()">
                    <span class="view-more-text">مشاهدة المزيد</span>
                    <span class="view-more-icon">👁️</span>
                </button>
                <div class="pagination-info" id="pagination-info">
                    <span>صفحة 1 من 92</span>
                </div>
            </div>
        </section>

        <!-- Blog Posts -->
        <section class="blog-section fade-in">
            <h3 class="section-title">
                📰 أحدث المقالات
            </h3>
            <div id="blog-posts-container">
                <!-- Blog posts will be loaded here -->
            </div>
        </section>
    </main>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="nav-items">
            <a href="#" class="nav-item active" data-page="home">
                <div class="nav-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                </div>
                <div class="nav-text">الرئيسية</div>
            </a>
            <a href="#" class="nav-item" data-page="categories">
                <div class="nav-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect width="7" height="7" x="3" y="3" rx="1"/>
                        <rect width="7" height="7" x="14" y="3" rx="1"/>
                        <rect width="7" height="7" x="14" y="14" rx="1"/>
                        <rect width="7" height="7" x="3" y="14" rx="1"/>
                    </svg>
                </div>
                <div class="nav-text">الفئات</div>
            </a>
            <a href="#" class="nav-item" data-page="search">
                <div class="nav-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="m21 21-4.35-4.35"/>
                    </svg>
                </div>
                <div class="nav-text">البحث</div>
            </a>
            <a href="#" class="nav-item" data-page="cart">
                <div class="nav-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="8" cy="21" r="1"/>
                        <circle cx="19" cy="21" r="1"/>
                        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57L23 6H6"/>
                    </svg>
                </div>
                <div class="nav-text">السلة</div>
                <span class="nav-badge" id="nav-cart-count" style="display: none;">0</span>
            </a>
            <a href="#" class="nav-item" data-page="profile">
                <div class="nav-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                </div>
                <div class="nav-text">حسابي</div>
            </a>
        </div>
    </nav>

    <!-- Scripts -->
    <script src="/js/mobile-pwa-features.js"></script>
    <script src="/js/advanced-mobile-features.js"></script>
    <script src="/js/pwa-integration.js"></script>
    
    <script>
        // Mobile App Configuration
        const APP_CONFIG = {
            apiBase: 'http://localhost:8000/api/v1',
            baseUrl: 'http://localhost:8000',
            locale: 'ar',
            currency: 'IQD',
            currencySymbol: 'IQD',
            debug: false // Set to true for debugging
        };

        // Override console.error for non-critical errors in production
        if (!APP_CONFIG.debug) {
            const originalConsoleError = console.error;
            console.error = function(...args) {
                const message = args.join(' ');
                // Only show critical errors
                if (message.includes('Failed to load resource') &&
                    (message.includes('404') || message.includes('auth/user') ||
                     message.includes('ajax/') || message.includes('icons-sprite.svg') ||
                     message.includes('pwa/') || message.includes('screenshot-desktop.png'))) {
                    return; // Suppress non-critical 404 errors
                }
                if (message.includes('Slider element not found') ||
                    message.includes('Error while trying to use the following icon')) {
                    return; // Suppress slider and icon errors
                }
                originalConsoleError.apply(console, args);
            };
        }

        // Mobile App Class
        class MobileApp {
            constructor() {
                this.currentPage = 'home';
                this.cartCount = 0;
                this.isLoading = false;
                this.currentSlide = 0;
                this.sliderData = [];
                this.sliderInterval = null;
                this.wishlistItems = new Set();

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.checkAuthStatus();
                this.loadInitialData();
                this.setupPWA();
            }

            setupEventListeners() {
                // Navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.navigateTo(item.dataset.page);
                    });
                });

                // Search
                document.getElementById('search-submit').addEventListener('click', () => {
                    this.performSearch();
                });

                document.getElementById('main-search').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });

                // Vehicle Parts Finder - with null check
                const vpfForm = document.getElementById('vpf-form');
                if (vpfForm) {
                    vpfForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.searchVehicleParts();
                    });
                }

                // Vehicle Parts Finder cascading selects - with null checks
                const vehicleTypeSelect = document.getElementById('vehicle-type');
                const vehicleBrandSelect = document.getElementById('vehicle-brand');
                const vehicleModelSelect = document.getElementById('vehicle-model');

                if (vehicleTypeSelect) {
                    vehicleTypeSelect.addEventListener('change', (e) => {
                        this.loadVehicleChildren(e.target.value, 'vehicle-brand');
                    });
                }

                if (vehicleBrandSelect) {
                    vehicleBrandSelect.addEventListener('change', (e) => {
                        this.loadVehicleChildren(e.target.value, 'vehicle-model');
                    });
                }

                if (vehicleModelSelect) {
                    vehicleModelSelect.addEventListener('change', (e) => {
                        this.loadVehicleChildren(e.target.value, 'vehicle-year');
                    });
                }

                // Quick actions
                document.querySelectorAll('.quick-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.quickSearch(item.dataset.category);
                    });
                });

                // Header buttons
                const cartBtn = document.getElementById('cart-btn');
                if (cartBtn) {
                    cartBtn.addEventListener('click', () => {
                        this.navigateTo('cart');
                    });
                }

                const userBtn = document.getElementById('user-btn');
                if (userBtn) {
                    userBtn.addEventListener('click', () => {
                        this.toggleUserMenu();
                    });
                }

                const menuBtn = document.getElementById('menu-btn');
                if (menuBtn) {
                    menuBtn.addEventListener('click', () => {
                        this.toggleMenu();
                    });
                }

                const notificationsBtn = document.getElementById('notifications-btn');
                if (notificationsBtn) {
                    notificationsBtn.addEventListener('click', () => {
                        this.showNotifications();
                    });
                }

                // Close user menu when clicking outside
                document.addEventListener('click', (e) => {
                    const userMenu = document.getElementById('user-menu-dropdown');
                    const userBtn = document.getElementById('user-btn');

                    if (userMenu && !userMenu.contains(e.target) && e.target !== userBtn) {
                        this.hideUserMenu();
                    }
                });

                // Slider controls - with null checks
                const sliderPrev = document.getElementById('slider-prev');
                const sliderNext = document.getElementById('slider-next');

                if (sliderPrev) {
                    sliderPrev.addEventListener('click', () => {
                        this.previousSlide();
                    });
                }

                if (sliderNext) {
                    sliderNext.addEventListener('click', () => {
                        this.nextSlide();
                    });
                }

                // Touch events for slider
                this.setupSliderTouchEvents();
            }

            async loadInitialData() {
                try {
                    this.showLoading(true);

                    // Show skeleton loaders
                    this.showSliderSkeleton();

                    // Load data in parallel for better performance
                    const promises = [
                        this.loadSimpleSliderShortcode().catch(e => {
                            console.error('Simple slider load failed:', e);
                            this.showSimpleSliderFallback();
                        }),
                        this.loadMainCategoriesShortcode().catch(e => {
                            console.error('Main categories load failed:', e);
                            this.showMainCategoriesFallback();
                        }),

                        this.loadFeaturedProducts().catch(e => console.error('Products load failed:', e)),
                        this.loadNewArrivals().catch(e => console.error('New arrivals load failed:', e)),
                        this.loadFlashSale().catch(e => console.error('Flash sale load failed:', e)),
                        this.loadBestSellingProducts().catch(e => console.error('Best selling products load failed:', e)),
                        this.loadAllProductsPreview().catch(e => console.error('All products preview load failed:', e)),
                        this.loadBlogPosts().catch(e => {
                            console.error('Blog posts load failed:', e);
                            // Hide blog section if loading fails
                            const blogSection = document.querySelector('.blog-section');
                            if (blogSection) {
                                blogSection.style.display = 'none';
                            }
                        }),
                        this.updateCartCount().catch(e => console.error('Cart count failed:', e)),
                        this.loadWishlist().catch(e => console.error('Wishlist load failed:', e)),
                        this.loadVehiclePartsFinderShortcode().catch(e => {
                            console.error('VPF load failed:', e);
                            this.loadCustomVPF();
                        })
                    ];

                    // Wait for all promises to complete (don't fail if one fails)
                    await Promise.allSettled(promises);

                    console.log('✅ Initial data loading completed');

                    // تنظيف الصور بعد التحميل
                    setTimeout(() => {
                        this.cleanupImageUrls();
                    }, 2000);

                } catch (error) {
                    console.error('Error loading initial data:', error);
                    // Don't show error to user for initial load failures
                } finally {
                    this.showLoading(false);
                    // Hide any remaining loading states
                    setTimeout(() => {
                        const loadingElements = document.querySelectorAll('.loading-container, .slider-loading, .categories-loading, .vpf-loading');
                        loadingElements.forEach(el => {
                            if (el && el.parentNode) {
                                el.style.display = 'none';
                            }
                        });
                    }, 2000);
                }
            }

            async loadSimpleSliderShortcode() {
                try {
                    // First try to load from API
                    const apiResponse = await fetch(`${APP_CONFIG.apiBase}/simple-sliders?keys[]=home-slider`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (apiResponse.ok) {
                        const apiData = await apiResponse.json();
                        if (!apiData.error && apiData.data && apiData.data.length > 0) {
                            const sliderData = apiData.data[0].items || [];
                            if (sliderData.length > 0) {
                                this.renderCustomSlider(sliderData);
                                console.log('✅ Simple slider loaded from API with', sliderData.length, 'items');
                                return;
                            }
                        }
                    }

                    // Fallback to shortcode
                    const response = await fetch(`${APP_CONFIG.baseUrl}/ajax/simple-slider-mobile`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'text/html',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        },
                        body: JSON.stringify({
                            shortcode: '[simple-slider style="full-width" key="home-slider" customize_font_family_of_description="0,1" font_family_of_description="Oregano" title_font_size="100" shape_1="photo-2025-03-14-23-53-47.jpg" shape_2="photo-2025-03-14-23-53-33-1.jpg" shape_3="photo-2025-03-14-23-52-59-1.jpg" shape_4="tsmym-ktaa-ghyar-3.jpg" is_autoplay="yes" autoplay_speed="8000" is_loop="yes" animation_enabled="yes"][/simple-slider]'
                        })
                    });

                    if (response.ok) {
                        const html = await response.text();
                        const container = document.getElementById('simple-slider-container');
                        if (container && html.trim()) {
                            container.innerHTML = html;
                            console.log('✅ Simple slider shortcode loaded successfully');
                            this.initializeSimpleSlider();
                        } else {
                            this.showSimpleSliderFallback();
                        }
                    } else {
                        this.showSimpleSliderFallback();
                    }
                } catch (error) {
                    console.error('Error loading simple slider:', error);
                    this.showSimpleSliderFallback();
                }
            }

            renderCustomSlider(slides) {
                const container = document.getElementById('simple-slider-container');
                if (!container || !slides.length) {
                    this.showSimpleSliderFallback();
                    return;
                }

                let currentSlide = 0;

                const sliderHTML = `
                    <div class="custom-slider" style="
                        position: relative;
                        height: 200px;
                        border-radius: 20px;
                        overflow: hidden;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    ">
                        <div class="slider-track" style="
                            display: flex;
                            height: 100%;
                            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                            width: ${slides.length * 100}%;
                        ">
                            ${slides.map((slide, index) => `
                                <div class="slide-item" style="
                                    flex: 0 0 ${100 / slides.length}%;
                                    width: ${100 / slides.length}%;
                                    height: 200px;
                                    position: relative;
                                    display: flex;
                                    align-items: center;
                                    background-image: url(${slide.image});
                                    background-size: cover;
                                    background-position: center;
                                    background-color: ${slide.background_color || '#0C55AA'};
                                ">
                                    <div class="slide-overlay" style="
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        right: 0;
                                        bottom: 0;
                                        background: ${slide.is_light ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.3)'};
                                        z-index: 1;
                                    "></div>
                                    <div class="slide-content" style="
                                        padding: 24px;
                                        color: ${slide.is_light ? '#333' : 'white'};
                                        z-index: 2;
                                        position: relative;
                                        max-width: 70%;
                                    ">
                                        ${slide.subtitle ? `<div style="font-size: 12px; margin-bottom: 6px; opacity: 0.9;">${slide.subtitle}</div>` : ''}
                                        <h3 style="font-size: 18px; font-weight: 700; line-height: 1.2; margin-bottom: 8px;">${slide.title}</h3>
                                        ${slide.description ? `<div style="font-size: 14px; line-height: 1.3; margin-bottom: 12px; opacity: 0.9;">${slide.description}</div>` : ''}
                                        ${slide.button_label && slide.link ? `<a href="${slide.link}" style="
                                            background: ${slide.is_light ? '#333' : '#fff'};
                                            color: ${slide.is_light ? '#fff' : '#333'};
                                            padding: 8px 16px;
                                            border-radius: 6px;
                                            font-size: 12px;
                                            font-weight: 600;
                                            text-decoration: none;
                                            display: inline-block;
                                            transition: all 0.3s ease;
                                        ">${slide.button_label}</a>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        ${slides.length > 1 ? `
                            <button class="slider-prev" style="
                                position: absolute;
                                left: 16px;
                                top: 50%;
                                transform: translateY(-50%);
                                width: 40px;
                                height: 40px;
                                background: rgba(255, 255, 255, 0.9);
                                border: none;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                                font-size: 18px;
                                color: #333;
                                z-index: 10;
                                transition: all 0.3s ease;
                            ">‹</button>

                            <button class="slider-next" style="
                                position: absolute;
                                right: 16px;
                                top: 50%;
                                transform: translateY(-50%);
                                width: 40px;
                                height: 40px;
                                background: rgba(255, 255, 255, 0.9);
                                border: none;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                                font-size: 18px;
                                color: #333;
                                z-index: 10;
                                transition: all 0.3s ease;
                            ">›</button>

                            <div class="slider-dots" style="
                                position: absolute;
                                bottom: 16px;
                                left: 50%;
                                transform: translateX(-50%);
                                display: flex;
                                gap: 8px;
                                z-index: 10;
                            ">
                                ${slides.map((_, index) => `
                                    <div class="slider-dot ${index === 0 ? 'active' : ''}" style="
                                        width: 8px;
                                        height: 8px;
                                        border-radius: 50%;
                                        background: ${index === 0 ? 'white' : 'rgba(255, 255, 255, 0.5)'};
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                    "></div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;

                container.innerHTML = sliderHTML;

                // Add CSS for proper slider display
                if (!document.getElementById('custom-slider-style')) {
                    const style = document.createElement('style');
                    style.id = 'custom-slider-style';
                    style.textContent = `
                        .custom-slider {
                            overflow: hidden !important;
                            position: relative !important;
                            width: 100% !important;
                        }
                        .slider-track {
                            display: flex !important;
                            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
                            width: ${slides.length * 100}% !important;
                            height: 100% !important;
                        }
                        .slide-item {
                            flex: 0 0 ${100 / slides.length}% !important;
                            width: ${100 / slides.length}% !important;
                            height: 200px !important;
                            flex-shrink: 0 !important;
                            background-repeat: no-repeat !important;
                            background-size: cover !important;
                            background-position: center !important;
                            position: relative !important;
                        }
                        .slider-prev:hover, .slider-next:hover {
                            background: rgba(255, 255, 255, 1) !important;
                            transform: translateY(-50%) scale(1.1) !important;
                        }
                        .slider-dot:hover {
                            background: rgba(255, 255, 255, 0.8) !important;
                        }
                    `;
                    document.head.appendChild(style);
                }

                // Initialize slider functionality
                if (slides.length > 1) {
                    this.initializeCustomSlider(slides.length);
                }
            }

            initializeCustomSlider(slideCount) {
                let currentSlide = 0;
                const track = document.querySelector('.slider-track');
                const dots = document.querySelectorAll('.slider-dot');
                const prevBtn = document.querySelector('.slider-prev');
                const nextBtn = document.querySelector('.slider-next');

                const goToSlide = (index) => {
                    currentSlide = index;
                    if (track) {
                        const slideWidth = 100 / slideCount;
                        track.style.transform = `translateX(-${index * slideWidth}%)`;
                    }

                    dots.forEach((dot, i) => {
                        dot.classList.toggle('active', i === index);
                        dot.style.background = i === index ? 'white' : 'rgba(255, 255, 255, 0.5)';
                    });
                };

                const nextSlide = () => {
                    const next = (currentSlide + 1) % slideCount;
                    goToSlide(next);
                };

                const prevSlide = () => {
                    const prev = currentSlide === 0 ? slideCount - 1 : currentSlide - 1;
                    goToSlide(prev);
                };

                // Event listeners
                if (nextBtn) nextBtn.addEventListener('click', nextSlide);
                if (prevBtn) prevBtn.addEventListener('click', prevSlide);

                dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => goToSlide(index));
                });

                // Auto-play
                const autoplayInterval = setInterval(nextSlide, 8000);

                // Store interval for cleanup
                if (window.sliderInterval) {
                    clearInterval(window.sliderInterval);
                }
                window.sliderInterval = autoplayInterval;

                console.log('🎠 Custom slider initialized with', slideCount, 'slides');
            }

            showSimpleSliderFallback() {
                const container = document.getElementById('simple-slider-container');
                if (container) {
                    container.innerHTML = `
                        <div style="
                            height: 200px;
                            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
                            border-radius: 20px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            text-align: center;
                            padding: 20px;
                        ">
                            <div>
                                <div style="width: 60px; height: 60px; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                                    <img src="/storage/main/general/logo-white.png" alt="دليل قطع الغيار" style="width: 100%; height: 100%; object-fit: contain;">
                                </div>
                                <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 8px;">دليل قطع الغيار</h2>
                                <p style="font-size: 16px; opacity: 0.9;">أفضل قطع الغيار بأسعار منافسة</p>
                            </div>
                        </div>
                    `;
                }
            }

            initializeSimpleSlider() {
                // Initialize any slider functionality if needed
                console.log('🎠 Simple slider initialized');
            }

            async loadMainCategoriesShortcode() {
                try {
                    // Load main categories from API
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/product-categories`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (!data.error && data.data) {
                            // Filter main categories (parent_id = 0 or null)
                            const mainCategories = data.data.filter(cat =>
                                cat.parent_id === 0 || cat.parent_id === null
                            ).slice(0, 6); // أول 6 فئات رئيسية

                            if (mainCategories.length > 0) {
                                this.renderMainCategories(mainCategories);
                                console.log('✅ Main categories loaded from API:', mainCategories.length);
                                return;
                            }
                        }
                    }

                    // Fallback if no main categories found
                    this.showMainCategoriesFallback();

                } catch (error) {
                    console.error('Error loading main categories from API:', error);
                    this.showMainCategoriesFallback();
                }
            }

            renderMainCategories(categories) {
                const container = document.getElementById('main-categories-container');
                if (!container || !categories.length) {
                    this.showMainCategoriesFallback();
                    return;
                }

                container.innerHTML = `
                    <div style="background: #F3F5F7; border-radius: 15px; padding: 20px;">
                        <h3 style="font-size: 18px; font-weight: 700; color: #333; margin-bottom: 16px; text-align: center;">
                            📂 الفئات الرئيسية
                        </h3>
                        <div id="main-categories-list" style="display: flex; gap: 12px; overflow-x: auto; padding: 4px 0; scrollbar-width: none; -ms-overflow-style: none;">
                            ${categories.map((category, index) => `
                                <div class="main-category-item" data-category-id="${category.id}" style="
                                    min-width: 80px;
                                    background: white;
                                    border-radius: 12px;
                                    padding: 16px 12px;
                                    text-align: center;
                                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                    transition: all 0.3s ease;
                                    cursor: pointer;
                                " onclick="window.mobileApp.toggleMainCategory(${category.id}, '${category.name}', '${category.slug}')">
                                    ${category.image_with_sizes && category.image_with_sizes.thumb ? `
                                        <div style="
                                            width: 40px;
                                            height: 40px;
                                            background-image: url(${category.image_with_sizes.thumb});
                                            background-size: cover;
                                            background-position: center;
                                            border-radius: 8px;
                                            margin: 0 auto 8px;
                                        "></div>
                                    ` : `
                                        <div style="
                                            width: 40px;
                                            height: 40px;
                                            background: ${this.getCategoryColor(index)};
                                            color: white;
                                            border-radius: 8px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin: 0 auto 8px;
                                            font-size: 20px;
                                        ">${this.getCategoryIcon(category.icon, index)}</div>
                                    `}
                                    <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif; line-height: 1.2;">
                                        ${category.name}
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        <!-- منطقة عرض الفئات الفرعية -->
                        <div id="subcategories-area" style="display: none; margin-top: 16px; padding-top: 16px; border-top: 1px solid #e0e0e0;">
                            <div id="subcategories-title" style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 12px;"></div>
                            <div id="subcategories-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 8px;"></div>
                        </div>
                    </div>
                `;
            }

            getCategoryColor(index) {
                const colors = [
                    '#0C55AA', // أزرق
                    '#dc3545', // أحمر
                    '#28a745', // أخضر
                    '#ffc107', // أصفر
                    '#6f42c1', // بنفسجي
                    '#fd7e14'  // برتقالي
                ];
                return colors[index % colors.length];
            }

            getCategoryIcon(iconClass, index) {
                // إذا كان هناك أيقونة من قاعدة البيانات
                if (iconClass && iconClass.includes('ti-')) {
                    // تحويل أيقونات Tabler إلى إيموجي
                    const iconMap = {
                        'ti-home': '🏠',
                        'ti-device-tv': '📺',
                        'ti-device-laptop': '💻',
                        'ti-device-mobile': '📱',
                        'ti-device-tablet': '📱',
                        'ti-camera': '📷',
                        'ti-gift': '🎁',
                        'ti-grill-spatula': '🍳',
                        'ti-building-store': '🏪',
                        'ti-ball-football': '⚽',
                        'ti-cpu-2': '🔧',
                        'ti-keyboard': '⌨️'
                    };
                    return iconMap[iconClass] || '📦';
                }

                // أيقونات افتراضية
                const defaultIcons = ['🔧', '🛑', '⚙️', '⚡', '🔩', '📦'];
                return defaultIcons[index % defaultIcons.length];
            }

            showMainCategoriesFallback() {
                const container = document.getElementById('main-categories-container');
                if (container) {
                    // الفئات الرئيسية الخمس كما طلبت
                    const mainCategories = [
                        { id: 1, name: 'أدوات صدر', icon: '🔧', color: '#0C55AA' },
                        { id: 2, name: 'بريك', icon: '🛑', color: '#dc3545' },
                        { id: 3, name: 'أدوات محرك', icon: '⚙️', color: '#28a745' },
                        { id: 4, name: 'أدوات كهربائية', icon: '⚡', color: '#ffc107' },
                        { id: 5, name: 'قطع الغيار ومنوع', icon: '🔩', color: '#6f42c1' }
                    ];

                    container.innerHTML = `
                        <div style="background: #F3F5F7; border-radius: 15px; padding: 20px;">
                            <h3 style="font-size: 18px; font-weight: 700; color: #333; margin-bottom: 16px; text-align: center;">
                                📂 الفئات الرئيسية
                            </h3>
                            <div id="main-categories-list" style="display: flex; gap: 12px; overflow-x: auto; padding: 4px 0; scrollbar-width: none; -ms-overflow-style: none;">
                                ${mainCategories.map(category => `
                                    <div class="main-category-item" data-category-id="${category.id}" style="
                                        min-width: 80px;
                                        background: white;
                                        border-radius: 12px;
                                        padding: 16px 12px;
                                        text-align: center;
                                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                        transition: all 0.3s ease;
                                        cursor: pointer;
                                    " onclick="window.mobileApp.toggleMainCategory(${category.id}, '${category.name}')">
                                        <div style="
                                            width: 40px;
                                            height: 40px;
                                            background: ${category.color};
                                            color: white;
                                            border-radius: 8px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin: 0 auto 8px;
                                            font-size: 20px;
                                        ">${category.icon}</div>
                                        <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif;">${category.name}</div>
                                    </div>
                                `).join('')}
                            </div>

                            <!-- منطقة عرض الفئات الفرعية -->
                            <div id="subcategories-area" style="display: none; margin-top: 16px; padding-top: 16px; border-top: 1px solid #e0e0e0;">
                                <div id="subcategories-title" style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 12px;"></div>
                                <div id="subcategories-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 8px;"></div>
                            </div>
                        </div>
                    `;
                }
            }

            initializeMainCategories() {
                // Initialize any categories functionality if needed
                console.log('📂 Main categories initialized');
            }

            async toggleMainCategory(categoryId, categoryName, categorySlug) {
                const subcategoriesArea = document.getElementById('subcategories-area');
                const subcategoriesTitle = document.getElementById('subcategories-title');
                const subcategoriesList = document.getElementById('subcategories-list');

                // إذا كانت الفئة مفتوحة بالفعل، أغلقها
                if (subcategoriesArea.style.display === 'block' &&
                    subcategoriesTitle.textContent.includes(categoryName)) {
                    subcategoriesArea.style.display = 'none';
                    return;
                }

                // عرض منطقة الفئات الفرعية
                subcategoriesArea.style.display = 'block';
                subcategoriesTitle.textContent = `الفئات الفرعية لـ ${categoryName}`;
                subcategoriesList.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">جاري التحميل...</div>';

                try {
                    // تحميل جميع الفئات من API
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/product-categories`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    // فلترة الفئات الفرعية للفئة المحددة
                    const subcategories = data.data.filter(cat =>
                        cat.parent_id === categoryId
                    ).slice(0, 8); // أول 8 فئات فرعية

                    if (subcategories.length === 0) {
                        // إذا لم توجد فئات فرعية، انتقل مباشرة لصفحة المنتجات
                        subcategoriesList.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                <div style="margin-bottom: 12px; color: #666;">
                                    لا توجد فئات فرعية، عرض جميع منتجات ${categoryName}
                                </div>
                                <button onclick="window.location.href='/mobile-products.html?category_id=${categoryId}&category_slug=${categorySlug}'" style="
                                    background: #0C55AA;
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 8px;
                                    font-weight: 600;
                                    cursor: pointer;
                                    font-family: 'Cairo', sans-serif;
                                ">
                                    عرض المنتجات
                                </button>
                            </div>
                        `;
                        return;
                    }

                    // عرض الفئات الفرعية مع تحميل عدد المنتجات
                    subcategoriesList.innerHTML = subcategories.map(subcat => `
                        <div class="subcategory-item" style="
                            background: white;
                            border: 1px solid #e0e0e0;
                            border-radius: 8px;
                            padding: 12px;
                            text-align: center;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            position: relative;
                        " onclick="window.location.href='/mobile-products.html?category_id=${subcat.id}&category_slug=${subcat.slug}'">
                            ${subcat.image_with_sizes && subcat.image_with_sizes.thumb ? `
                                <div style="
                                    width: 40px;
                                    height: 40px;
                                    background-image: url(${subcat.image_with_sizes.thumb});
                                    background-size: cover;
                                    background-position: center;
                                    border-radius: 8px;
                                    margin: 0 auto 8px;
                                "></div>
                            ` : `
                                <div style="
                                    width: 40px;
                                    height: 40px;
                                    background: #f8f9fa;
                                    border-radius: 8px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    margin: 0 auto 8px;
                                    font-size: 20px;
                                ">📦</div>
                            `}
                            <div style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 4px; line-height: 1.2;">
                                ${subcat.name}
                            </div>
                            <div style="font-size: 12px; color: #666;" id="products-count-${subcat.id}">
                                جاري التحميل...
                            </div>
                        </div>
                    `).join('');

                    // تحميل عدد المنتجات لكل فئة فرعية
                    subcategories.forEach(async (subcat) => {
                        try {
                            const productsResponse = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?categories[]=${subcat.id}&per_page=1`);
                            const productsData = await productsResponse.json();

                            const countElement = document.getElementById(`products-count-${subcat.id}`);
                            if (countElement && !productsData.error) {
                                const totalProducts = productsData.meta?.total || 0;
                                countElement.textContent = `${totalProducts} منتج`;
                            } else if (countElement) {
                                countElement.textContent = '0 منتج';
                            }
                        } catch (error) {
                            const countElement = document.getElementById(`products-count-${subcat.id}`);
                            if (countElement) {
                                countElement.textContent = '0 منتج';
                            }
                        }
                    });

                    // إضافة تأثيرات hover
                    if (!document.getElementById('subcategory-hover-style')) {
                        const style = document.createElement('style');
                        style.id = 'subcategory-hover-style';
                        style.textContent = `
                            .subcategory-item:hover {
                                border-color: #0C55AA !important;
                                transform: translateY(-2px);
                                box-shadow: 0 4px 12px rgba(12, 85, 170, 0.15);
                            }
                        `;
                        document.head.appendChild(style);
                    }

                } catch (error) {
                    console.error('Error loading subcategories:', error);
                    subcategoriesList.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #dc3545;">
                            حدث خطأ في تحميل الفئات الفرعية
                        </div>
                    `;
                }
            }



            // Legacy slider method - kept for compatibility
            async loadSlider() {
                console.log('Legacy slider method called - using simple slider shortcode instead');
                return Promise.resolve();
            }

            async loadNewArrivals() {
                try {
                    console.log('🔄 Loading new arrivals...');

                    // تحميل أحدث 8 منتجات
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?limit=8`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    console.log('📡 New arrivals response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('📦 New arrivals data:', data);

                    if (!data.error && data.data && data.data.length > 0) {
                        const processedProducts = this.processProductsData(data.data);
                        console.log('✅ Processed new arrivals:', processedProducts);

                        this.renderProducts(processedProducts, 'new-arrivals');
                        console.log('✅ New arrivals rendered successfully:', processedProducts.length);
                    } else {
                        console.log('❌ No new arrivals data found');
                        // Hide new arrivals section if no data
                        const newArrivalsSection = document.querySelector('.new-arrivals-section');
                        if (newArrivalsSection) {
                            newArrivalsSection.style.display = 'none';
                        }
                    }
                } catch (error) {
                    console.error('❌ Error loading new arrivals:', error);
                    // Hide new arrivals section if loading fails
                    const newArrivalsSection = document.querySelector('.new-arrivals-section');
                    if (newArrivalsSection) {
                        newArrivalsSection.style.display = 'none';
                    }
                }
            }

            async loadFlashSale() {
                try {
                    // Try to load flash sales using the correct API endpoint
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/flash-sales`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    const data = await response.json();

                    if (!data.error && data.data && data.data.length > 0) {
                        document.getElementById('flash-sale-section').style.display = 'block';
                        this.renderFlashSaleProducts(data.data);
                        console.log('✅ Flash sale loaded:', data.data.length);
                    } else {
                        // Hide flash sale section if no data
                        document.getElementById('flash-sale-section').style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error loading flash sale:', error);
                    // Hide flash sale section if loading fails
                    document.getElementById('flash-sale-section').style.display = 'none';
                }
            }



            async loadBlogPosts() {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/blog/posts?limit=3`);
                    const data = await response.json();

                    if (!data.error && data.data) {
                        this.renderBlogPosts(data.data);
                        console.log('✅ Blog posts loaded:', data.data.length);
                    }
                } catch (error) {
                    console.error('Error loading blog posts:', error);
                    // Hide blog section if no posts
                    const blogSection = document.querySelector('.blog-section');
                    if (blogSection) {
                        blogSection.style.display = 'none';
                    }
                }
            }

            renderSlider(slides) {
                const track = document.getElementById('slider-track');
                const dots = document.getElementById('slider-dots');

                if (!track || !slides.length) {
                    this.hideSlider();
                    return;
                }

                // Clear existing content
                track.innerHTML = '';
                dots.innerHTML = '';

                // Render slides using the actual API data structure
                slides.forEach((slide, index) => {
                    const slideElement = document.createElement('div');
                    slideElement.className = 'slider-item';

                    // Set background image from API
                    if (slide.image) {
                        slideElement.style.backgroundImage = `url(${slide.image})`;
                    }

                    // Set background color from metadata
                    if (slide.background_color) {
                        slideElement.style.backgroundColor = slide.background_color;
                    }

                    // Determine if content should be light or dark based on API data
                    const isLight = slide.is_light === 1 || slide.is_light === true;

                    // Build slide content HTML
                    let contentHTML = `<div class="slider-content ${isLight ? 'light' : ''}">`;

                    // Add subtitle if exists
                    if (slide.subtitle) {
                        contentHTML += `<div class="slider-subtitle">${slide.subtitle}</div>`;
                    }

                    // Add title (required field)
                    if (slide.title) {
                        contentHTML += `<h3 class="slider-title">${slide.title}</h3>`;
                    }

                    // Add description if exists
                    if (slide.description) {
                        contentHTML += `<div class="slider-description">${slide.description}</div>`;
                    }

                    // Add button if both label and link exist
                    if (slide.button_label && slide.link) {
                        contentHTML += `<a href="${slide.link}" class="slider-btn">${slide.button_label}</a>`;
                    }

                    contentHTML += '</div>';
                    contentHTML += '<div class="slider-overlay"></div>';

                    slideElement.innerHTML = contentHTML;
                    track.appendChild(slideElement);

                    // Create navigation dot
                    const dot = document.createElement('div');
                    dot.className = `slider-dot ${index === 0 ? 'active' : ''}`;
                    dot.addEventListener('click', () => this.goToSlide(index));
                    dots.appendChild(dot);
                });

                console.log(`Rendered ${slides.length} slider items`);
            }

            hideSlider() {
                const sliderSection = document.querySelector('.slider-section');
                if (sliderSection) {
                    sliderSection.style.display = 'none';
                    console.log('Slider hidden - no data available');
                }
            }

            showSliderSkeleton() {
                const container = document.getElementById('simple-slider-container');
                if (container) {
                    container.innerHTML = `
                        <div class="slider-skeleton" style="
                            height: 200px;
                            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                            background-size: 200% 100%;
                            animation: shimmer 1.5s infinite;
                            border-radius: 20px;
                        "></div>
                    `;
                } else {
                    console.log('Slider container element not found for skeleton');
                }
            }

            goToSlide(index) {
                if (index < 0 || index >= this.sliderData.length) return;

                this.currentSlide = index;
                const track = document.getElementById('slider-track');
                const dots = document.querySelectorAll('.slider-dot');

                if (track) {
                    // Fix: Use proper percentage calculation for multiple slides
                    const translateX = -index * 100;
                    track.style.transform = `translateX(${translateX}%)`;
                    track.style.transition = 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                }

                // Update dots
                dots.forEach((dot, i) => {
                    dot.classList.toggle('active', i === index);
                });

                console.log(`Moved to slide ${index + 1} of ${this.sliderData.length}`);
            }

            nextSlide() {
                if (!this.sliderData || this.sliderData.length === 0) return;
                const nextIndex = (this.currentSlide + 1) % this.sliderData.length;
                this.goToSlide(nextIndex);
            }

            previousSlide() {
                if (!this.sliderData || this.sliderData.length === 0) return;
                const prevIndex = this.currentSlide === 0 ? this.sliderData.length - 1 : this.currentSlide - 1;
                this.goToSlide(prevIndex);
            }

            startSliderAutoplay() {
                this.stopSliderAutoplay();
                if (this.sliderData.length > 1) {
                    this.sliderInterval = setInterval(() => {
                        this.nextSlide();
                    }, 5000); // Change slide every 5 seconds
                    console.log('Slider autoplay started');
                }
            }

            stopSliderAutoplay() {
                if (this.sliderInterval) {
                    clearInterval(this.sliderInterval);
                    this.sliderInterval = null;
                }
            }

            setupSliderTouchEvents() {
                const slider = document.querySelector('.custom-slider') || document.getElementById('main-slider');
                if (!slider) {
                    console.log('Slider element not found, skipping touch events setup');
                    return;
                }

                let startX = 0;
                let startY = 0;
                let isDragging = false;

                slider.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    isDragging = true;
                    this.stopSliderAutoplay();
                }, { passive: true });

                slider.addEventListener('touchmove', (e) => {
                    if (!isDragging) return;

                    const currentX = e.touches[0].clientX;
                    const currentY = e.touches[0].clientY;
                    const diffX = startX - currentX;
                    const diffY = startY - currentY;

                    // Prevent vertical scrolling if horizontal swipe is detected
                    if (Math.abs(diffX) > Math.abs(diffY)) {
                        e.preventDefault();
                    }
                }, { passive: false });

                slider.addEventListener('touchend', (e) => {
                    if (!isDragging) return;

                    const endX = e.changedTouches[0].clientX;
                    const diffX = startX - endX;

                    if (Math.abs(diffX) > 50) { // Minimum swipe distance
                        if (diffX > 0) {
                            this.nextSlide();
                        } else {
                            this.previousSlide();
                        }
                    }

                    isDragging = false;
                    this.startSliderAutoplay();
                }, { passive: true });
            }

            async loadCategories() {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/product-categories`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message || 'Error loading categories');
                    }

                    // Use actual API data structure
                    const categories = data.data || [];
                    this.renderCategories(categories);

                    console.log(`Loaded ${categories.length} categories`);
                } catch (error) {
                    console.error('Error loading categories:', error);
                    // Don't show error to user for categories, just log it
                }
            }

            renderCategories(categories) {
                const container = document.getElementById('categories-container');
                if (!container) return;

                container.innerHTML = '';

                // Show only first 10 categories for mobile display
                categories.slice(0, 10).forEach(category => {
                    const item = document.createElement('a');
                    item.className = 'category-item';
                    item.href = '#';
                    item.dataset.categoryId = category.id;

                    // Use category image if available, otherwise use default icon
                    const categoryImage = category.image ?
                        `<img src="${category.image}" alt="${category.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;">` :
                        '📦';

                    item.innerHTML = `
                        <div class="category-img">
                            ${categoryImage}
                        </div>
                        <div class="category-name">${category.name}</div>
                    `;

                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.viewCategory(category.id, category.slug);
                    });

                    container.appendChild(item);
                });
            }

            async loadFeaturedProducts() {
                try {
                    // Load featured products with higher limit for better display
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?featured=1&limit=12`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message || 'Error loading products');
                    }

                    if (data.data && data.data.length > 0) {
                        // Process products with proper pricing
                        const processedProducts = this.processProductsData(data.data);
                        this.renderProducts(processedProducts, 'featured-products');
                        console.log(`✅ Loaded ${processedProducts.length} featured products`);
                    } else {
                        // Try loading regular products if no featured products
                        this.loadRegularProducts('featured-products', 12);
                    }
                } catch (error) {
                    console.error('Error loading featured products:', error);
                    // Try loading regular products as fallback
                    this.loadRegularProducts('featured-products', 12);
                }
            }

            processProductsData(products) {
                return products.map(product => {
                    // معالجة الصور - استخدام placeholder للصور المفقودة
                    let imageUrl = '/storage/main/general/placeholder.png'; // الافتراضي

                    if (product.image) {
                        imageUrl = product.image;
                    } else if (product.image_url) {
                        imageUrl = product.image_url;
                    } else if (product.images && product.images.length > 0) {
                        imageUrl = product.images[0];
                    } else if (product.thumbnail) {
                        imageUrl = product.thumbnail;
                    } else if (product.featured_image) {
                        imageUrl = product.featured_image;
                    }

                    // إزالة APP_URL إذا كان موجود لتجنب مشاكل 403
                    if (imageUrl && imageUrl.startsWith('http://localhost:8000')) {
                        imageUrl = imageUrl.replace('http://localhost:8000', '');
                    }

                    // إصلاح مسار الصور - معالجة شاملة للمسارات
                    if (imageUrl && !imageUrl.startsWith('http')) {
                        if (imageUrl.startsWith('/storage/')) {
                            // المسار صحيح بالفعل
                            imageUrl = imageUrl;
                        } else if (imageUrl.startsWith('storage/')) {
                            // إضافة / في البداية
                            imageUrl = '/' + imageUrl;
                        } else if (imageUrl.includes('-150x150.') || imageUrl.includes('-600x600.')) {
                            // ملفات الصور المصغرة - تحقق من وجودها أولاً
                            if (imageUrl.match(/\d{4}-150x150\.(png|PNG|jpg|jpeg)$/)) {
                                imageUrl = '/storage/main/general/placeholder.png';
                            } else {
                                imageUrl = '/storage/' + imageUrl;
                            }
                        } else if (!imageUrl.startsWith('/')) {
                            // إضافة المسار الكامل
                            imageUrl = '/storage/' + imageUrl;
                        }
                    }

                    // التحقق من صحة مسار الصورة
                    imageUrl = this.validateImagePath(imageUrl);

                    console.log(`🖼️ Product ${product.id} final image:`, imageUrl);

                    // Process pricing data to match our system
                    const processedProduct = {
                        ...product,
                        image: imageUrl,
                        price_formatted: this.formatPrice(product.price || 0),
                        original_price_formatted: product.original_price ? this.formatPrice(product.original_price) : null,
                        wholesale_price_formatted: product.wholesale_price ? this.formatPrice(product.wholesale_price) : null
                    };

                    return processedProduct;
                });
            }

            formatPrice(price) {
                if (!price || price <= 0) return 'السعر عند الطلب';
                return new Intl.NumberFormat('ar-IQ', {
                    style: 'currency',
                    currency: 'IQD',
                    minimumFractionDigits: 0
                }).format(price).replace('IQD', 'IQD');
            }

            // دالة للتحقق من صحة مسار الصورة
            validateImagePath(imagePath) {
                // إذا كانت الصورة تحتوي على أرقام فقط مع امتداد، فهي غالباً مفقودة
                if (imagePath && imagePath.match(/\d{4}-150x150\.(png|PNG|jpg|jpeg)$/)) {
                    console.log(`🚫 Detected missing image pattern: ${imagePath}, using placeholder`);
                    return '/storage/main/general/placeholder.png';
                }

                // إزالة أي APP_URL متبقي
                if (imagePath && imagePath.includes('localhost:8000')) {
                    imagePath = imagePath.replace(/https?:\/\/localhost:8000\/?/, '');
                }

                return imagePath || '/storage/main/general/placeholder.png';
            }

            // دالة لتنظيف جميع الصور بعد التحميل
            cleanupImageUrls() {
                const images = document.querySelectorAll('img');
                images.forEach(img => {
                    if (img.src && img.src.includes('localhost:8000/storage/') && img.src.includes('-150x150.')) {
                        console.log(`🧹 Cleaning up image: ${img.src}`);
                        img.src = '/storage/main/general/placeholder.png';
                    }
                });
            }

            async loadRegularProducts(containerId, limit = 12) {
                try {
                    // Load regular products as fallback
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?limit=${limit}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (!data.error && data.data && data.data.length > 0) {
                        const processedProducts = this.processProductsData(data.data);
                        this.renderProducts(processedProducts, containerId);
                        console.log(`✅ Loaded ${processedProducts.length} regular products for ${containerId}`);
                    } else {
                        // Hide section if no products available
                        const section = document.querySelector(`#${containerId}`).closest('section');
                        if (section) {
                            section.style.display = 'none';
                        }
                    }
                } catch (error) {
                    console.error(`Error loading regular products for ${containerId}:`, error);
                    // Hide section if loading fails
                    const section = document.querySelector(`#${containerId}`).closest('section');
                    if (section) {
                        section.style.display = 'none';
                    }
                }
            }



            renderProducts(products, containerId = 'featured-products') {
                const container = document.getElementById(containerId);
                if (!container) return;

                container.innerHTML = '';

                if (!products || products.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">📦</div>
                            <p class="empty-text">لا توجد منتجات متاحة حالياً</p>
                        </div>
                    `;
                    return;
                }

                products.forEach(product => {
                    const card = document.createElement('a');
                    card.className = 'product-card';
                    card.href = '#';
                    card.dataset.productId = product.id;
                    card.onclick = () => this.viewProduct(product.id);

                    const discount = product.original_price > product.price && product.price > 0 ?
                        Math.round((1 - product.price / product.original_price) * 100) : 0;
                    
                    card.innerHTML = `
                        <div class="product-img">
                            <img src="${product.image || '/storage/main/general/placeholder.png'}"
                                 alt="${product.name}" loading="lazy"
                                 onerror="console.log('❌ Image failed:', this.src); this.onerror=null; this.src='/storage/main/general/placeholder.png';"
                                 onload="console.log('✅ Image loaded successfully:', this.src)"
                            ${discount > 0 ? `<div class="product-badge">-${discount}%</div>` : ''}
                        </div>
                        <div class="product-info">
                            <div class="product-name">${product.name}</div>
                            <div class="product-price">
                                <span class="price-current">${this.formatPrice(product.sale_price || product.price)}</span>
                                ${product.sale_price ? `<span class="price-old">${this.formatPrice(product.price)}</span>` : ''}
                            </div>
                            <div class="product-actions">
                                <button class="btn-cart" onclick="app.addToCart(${product.id}, event)">
                                    إضافة للسلة
                                </button>
                                <button class="btn-wishlist" data-product-id="${product.id}" onclick="app.toggleWishlist(${product.id}, event)">
                                    🤍
                                </button>
                            </div>
                        </div>
                    `;
                    
                    card.addEventListener('click', (e) => {
                        if (!e.target.closest('.product-actions')) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('Navigating to product:', product.id);
                            this.viewProduct(product.id);
                        }
                    });
                    
                    container.appendChild(card);
                });
            }

            renderProductsGrid(products, containerId) {
                this.renderProducts(products, containerId);
            }

            renderFlashSaleProducts(products) {
                const container = document.getElementById('flash-sale-container');
                if (!container) return;

                // معالجة البيانات أولاً
                const processedProducts = this.processProductsData(products);

                container.innerHTML = `
                    <div class="flash-sale-grid">
                        ${processedProducts.map(product => `
                            <div class="flash-sale-item" onclick="app.viewProduct('${product.id}')">
                                <div class="flash-sale-image">
                                    <img src="${product.image || '/vendor/core/core/base/images/placeholder.png'}"
                                         alt="${product.name}"
                                         loading="lazy"
                                         onerror="this.src='/vendor/core/core/base/images/placeholder.png'">
                                    <div class="flash-sale-badge">
                                        🔥 ${product.original_price > product.price ?
                                            Math.round(((product.original_price - product.price) / product.original_price) * 100) + '% خصم' :
                                            'عرض خاص'
                                        }
                                    </div>
                                </div>
                                <div class="flash-sale-info">
                                    <h4 class="flash-sale-name">${product.name}</h4>
                                    <div class="flash-sale-price">
                                        ${this.renderProductPrice(product)}
                                    </div>
                                    <button class="flash-sale-btn" onclick="app.addToCart(${product.id}, event)">
                                        ⚡ اشتري الآن
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            renderBlogPosts(posts) {
                const container = document.getElementById('blog-posts-container');
                if (!container) return;

                if (!posts || posts.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">📰</div>
                            <p class="empty-text">لا توجد مقالات متاحة حالياً</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = `
                    <div class="blog-grid">
                        ${posts.map(post => `
                            <article class="blog-card" onclick="window.open('${post.url || '#'}', '_blank')">
                                <div class="blog-image">
                                    <img src="${post.image || '/vendor/core/core/base/images/placeholder.png'}"
                                         alt="${post.name}"
                                         loading="lazy"
                                         onerror="this.src='/vendor/core/core/base/images/placeholder.png'">
                                </div>
                                <div class="blog-content">
                                    <h4 class="blog-title">${post.name}</h4>
                                    <p class="blog-excerpt">${post.description || ''}</p>
                                    <div class="blog-meta">
                                        <span class="blog-date">${new Date(post.created_at).toLocaleDateString('ar-EG')}</span>
                                    </div>
                                </div>
                            </article>
                        `).join('')}
                    </div>
                `;
            }

            renderProductPrice(product) {
                if (product.price <= 0) {
                    return '<span class="price-on-request">السعر عند الطلب</span>';
                }

                let priceHtml = '';
                const isWholesaleCustomer = this.currentUser && this.currentUser.is_wholesale;
                const isAdmin = this.currentUser && this.currentUser.is_admin;

                // الأدمن يرى جميع الأسعار
                if (isAdmin) {
                    // سعر المفرد
                    if (product.original_price > product.price) {
                        priceHtml += `
                            <div class="price-row">
                                <span class="price-label">المفرد:</span>
                                <span class="current-price">${product.price_formatted}</span>
                                <span class="original-price">${product.original_price_formatted}</span>
                            </div>
                        `;
                    } else {
                        priceHtml += `
                            <div class="price-row">
                                <span class="price-label">المفرد:</span>
                                <span class="current-price">${product.price_formatted}</span>
                            </div>
                        `;
                    }

                    // سعر الجملة (للأدمن)
                    if (product.wholesale_price && product.wholesale_price > 0) {
                        priceHtml += `
                            <div class="price-row wholesale">
                                <span class="price-label">الجملة:</span>
                                <span class="wholesale-price">${product.wholesale_price_formatted || (product.wholesale_price + ' IQD')}</span>
                                <span class="admin-badge">👑 أدمن</span>
                            </div>
                        `;
                    }
                }
                // عميل الجملة يرى سعر الجملة فقط
                else if (isWholesaleCustomer && product.wholesale_price && product.wholesale_price > 0) {
                    priceHtml += `
                        <div class="price-row wholesale">
                            <span class="wholesale-price">${product.wholesale_price_formatted || (product.wholesale_price + ' IQD')}</span>
                            <span class="wholesale-badge">✓ عميل جملة</span>
                        </div>
                    `;
                }
                // عميل المفرد أو غير مسجل يرى سعر المفرد فقط
                else {
                    if (product.original_price > product.price) {
                        priceHtml += `
                            <div class="price-row">
                                <span class="current-price">${product.price_formatted}</span>
                                <span class="original-price">${product.original_price_formatted}</span>
                            </div>
                        `;
                    } else {
                        priceHtml += `
                            <div class="price-row">
                                <span class="current-price">${product.price_formatted}</span>
                            </div>
                        `;
                    }

                    // إظهار رسالة للتسجيل كعميل جملة (فقط للغير مسجلين)
                    if (!this.isAuthenticated && product.wholesale_price && product.wholesale_price > 0) {
                        priceHtml += `
                            <div class="price-row wholesale-info">
                                <span class="wholesale-info-text">💼 <a href="/mobile-register.html">سجل كعميل جملة</a> للحصول على أسعار خاصة</span>
                            </div>
                        `;
                    }
                }

                return priceHtml;
            }

            // Navigation methods
            navigateTo(page) {
                // Update active nav item
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-page="${page}"]`).classList.add('active');
                
                this.currentPage = page;
                
                // Handle page-specific logic
                switch (page) {
                    case 'home':
                        this.loadInitialData();
                        break;
                    case 'categories':
                        this.showCategoriesPage();
                        break;
                    case 'search':
                        this.showSearchPage();
                        break;
                    case 'cart':
                        this.showCartPage();
                        break;
                    case 'profile':
                        this.showProfilePage();
                        break;
                }
            }

            // Search methods
            performSearch() {
                const query = document.getElementById('main-search').value.trim();
                if (query) {
                    window.location.href = `${APP_CONFIG.baseUrl}/products?search=${encodeURIComponent(query)}`;
                }
            }

            quickSearch(category) {
                window.location.href = `${APP_CONFIG.baseUrl}/products?category=${category}`;
            }

            // Vehicle Parts Finder
            async loadVehiclePartsFinderShortcode() {
                try {
                    // Load the actual shortcode from the server
                    const response = await fetch(`${APP_CONFIG.baseUrl}/ajax/vehicle-parts-finder-mobile`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'text/html',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    if (response.ok) {
                        const html = await response.text();
                        const vpfSection = document.getElementById('vpf-section');
                        if (vpfSection && html.trim()) {
                            vpfSection.innerHTML = html;
                            console.log('✅ Vehicle Parts Finder shortcode loaded successfully');

                            // Initialize the VPF functionality after loading
                            this.initializeVPFEvents();
                        } else {
                            // Fallback to custom implementation
                            this.loadCustomVPF();
                        }
                    } else {
                        // Fallback to custom implementation
                        this.loadCustomVPF();
                    }
                } catch (error) {
                    console.error('Error loading VPF shortcode:', error);
                    this.loadCustomVPF();
                }
            }

            loadCustomVPF() {
                const vpfSection = document.getElementById('vpf-section');
                if (vpfSection) {
                    vpfSection.innerHTML = `
                        <div class="vpf-header" style="text-align: center; margin-bottom: 20px;">
                            <div class="vpf-icon" style="
                                width: 60px;
                                height: 60px;
                                background: rgba(255, 255, 255, 0.15);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 12px;
                                font-size: 28px;
                            ">
                        <img src="/storage/main/general/logo-white.png" alt="دليل قطع الغيار" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                            <h2 class="vpf-title" style="
                                font-size: 20px;
                                font-weight: 700;
                                margin-bottom: 8px;
                                color: white;
                            ">البحث عن قطع الغيار</h2>
                            <p class="vpf-subtitle" style="
                                font-size: 14px;
                                opacity: 0.9;
                                color: white;
                            ">اختر سيارتك للعثور على القطع المناسبة</p>
                        </div>
                        <form class="vpf-form" id="vpf-form" style="display: grid; gap: 12px;">
                            <select class="vpf-select" id="vehicle-type" data-level="1" required style="
                                background: rgba(255, 255, 255, 0.1);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                border-radius: 12px;
                                padding: 14px 16px;
                                color: white;
                                font-size: 16px;
                                font-family: 'Cairo', sans-serif;
                                backdrop-filter: blur(10px);
                                transition: all 0.3s ease;
                            ">
                                <option value="" style="background: #0C55AA; color: white;">النوع</option>
                            </select>
                            <select class="vpf-select" id="vehicle-brand" data-level="2" disabled style="
                                background: rgba(255, 255, 255, 0.1);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                border-radius: 12px;
                                padding: 14px 16px;
                                color: white;
                                font-size: 16px;
                                font-family: 'Cairo', sans-serif;
                                backdrop-filter: blur(10px);
                                transition: all 0.3s ease;
                            ">
                                <option value="" style="background: #0C55AA; color: white;">الماركة</option>
                            </select>
                            <select class="vpf-select" id="vehicle-model" data-level="3" disabled style="
                                background: rgba(255, 255, 255, 0.1);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                border-radius: 12px;
                                padding: 14px 16px;
                                color: white;
                                font-size: 16px;
                                font-family: 'Cairo', sans-serif;
                                backdrop-filter: blur(10px);
                                transition: all 0.3s ease;
                            ">
                                <option value="" style="background: #0C55AA; color: white;">الموديل</option>
                            </select>
                            <select class="vpf-select" id="vehicle-year" data-level="4" disabled style="
                                background: rgba(255, 255, 255, 0.1);
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                border-radius: 12px;
                                padding: 14px 16px;
                                color: white;
                                font-size: 16px;
                                font-family: 'Cairo', sans-serif;
                                backdrop-filter: blur(10px);
                                transition: all 0.3s ease;
                            ">
                                <option value="" style="background: #0C55AA; color: white;">السنة</option>
                            </select>
                            <button type="submit" class="vpf-btn" style="
                                background: white;
                                color: #0C55AA;
                                border: none;
                                padding: 16px;
                                border-radius: 12px;
                                font-size: 16px;
                                font-weight: 700;
                                font-family: 'Cairo', sans-serif;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                margin-top: 8px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                gap: 8px;
                            ">
                                🔍 عرض القطع المتاحة
                            </button>
                        </form>
                    `;

                    // Add hover effects
                    const style = document.createElement('style');
                    style.textContent = `
                        .vpf-select:focus {
                            outline: none !important;
                            border-color: rgba(255, 255, 255, 0.5) !important;
                            background: rgba(255, 255, 255, 0.15) !important;
                        }
                        .vpf-btn:hover {
                            background: #f0f0f0 !important;
                            transform: translateY(-2px);
                            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
                        }
                    `;
                    document.head.appendChild(style);

                    // Load root categories for custom VPF
                    this.loadVehicleRootCategories();

                    // Setup event listeners
                    this.setupCustomVPFEvents();
                }
            }

            initializeVPFEvents() {
                // This will be called after the shortcode is loaded
                // The shortcode should have its own JavaScript initialization
                console.log('🔧 VPF Events initialized');
            }

            setupCustomVPFEvents() {
                // Setup events for custom VPF
                const vpfForm = document.getElementById('vpf-form');
                if (vpfForm) {
                    vpfForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.searchVehicleParts();
                    });
                }

                // Setup cascading selects
                const vehicleType = document.getElementById('vehicle-type');
                const vehicleBrand = document.getElementById('vehicle-brand');
                const vehicleModel = document.getElementById('vehicle-model');

                if (vehicleType) {
                    vehicleType.addEventListener('change', (e) => {
                        this.loadVehicleChildren(e.target.value, 'vehicle-brand');
                    });
                }

                if (vehicleBrand) {
                    vehicleBrand.addEventListener('change', (e) => {
                        this.loadVehicleChildren(e.target.value, 'vehicle-model');
                    });
                }

                if (vehicleModel) {
                    vehicleModel.addEventListener('change', (e) => {
                        this.loadVehicleChildren(e.target.value, 'vehicle-year');
                    });
                }
            }

            async loadVehicleRootCategories() {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/vehicle-parts/root-categories`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message || 'Error loading vehicle types');
                    }

                    const typeSelect = document.getElementById('vehicle-type');
                    if (typeSelect && data.data) {
                        typeSelect.innerHTML = '<option value="">النوع</option>';
                        data.data.forEach(category => {
                            typeSelect.innerHTML += `<option value="${category.id}">${category.name}</option>`;
                        });
                        console.log(`Loaded ${data.data.length} vehicle types`);
                    }
                } catch (error) {
                    console.error('Error loading vehicle root categories:', error);
                }
            }

            async loadVehicleChildren(parentId, targetSelectId) {
                if (!parentId) {
                    this.resetDependentSelects(targetSelectId);
                    return;
                }

                const targetSelect = document.getElementById(targetSelectId);
                if (!targetSelect) return;

                try {
                    targetSelect.innerHTML = '<option value="">جاري التحميل...</option>';
                    targetSelect.disabled = true;

                    // Reset all dependent selects
                    this.resetDependentSelects(targetSelectId);

                    const response = await fetch(`${APP_CONFIG.apiBase}/vehicle-parts/categories/${parentId}/children`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message || 'Error loading data');
                    }

                    // Get the appropriate label
                    const labels = {
                        'vehicle-brand': 'الماركة',
                        'vehicle-model': 'الموديل',
                        'vehicle-year': 'السنة'
                    };

                    targetSelect.innerHTML = `<option value="">اختر ${labels[targetSelectId]}</option>`;

                    if (data.data && data.data.length > 0) {
                        data.data.forEach(item => {
                            targetSelect.innerHTML += `<option value="${item.id}">${item.name}</option>`;
                        });
                        targetSelect.disabled = false;
                        console.log(`Loaded ${data.data.length} items for ${targetSelectId}`);
                    } else {
                        targetSelect.innerHTML = `<option value="">لا توجد ${labels[targetSelectId]} متاحة</option>`;
                    }

                } catch (error) {
                    console.error(`Error loading ${targetSelectId}:`, error);
                    targetSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
                }
            }

            resetDependentSelects(currentSelectId) {
                const selectOrder = ['vehicle-type', 'vehicle-brand', 'vehicle-model', 'vehicle-year'];
                const currentIndex = selectOrder.indexOf(currentSelectId);

                // Reset all selects after the current one
                for (let i = currentIndex + 1; i < selectOrder.length; i++) {
                    const select = document.getElementById(selectOrder[i]);
                    if (select) {
                        const labels = {
                            'vehicle-brand': 'الماركة',
                            'vehicle-model': 'الموديل',
                            'vehicle-year': 'السنة'
                        };
                        select.innerHTML = `<option value="">${labels[selectOrder[i]]}</option>`;
                        select.disabled = true;
                    }
                }
            }

            searchVehicleParts() {
                const typeSelect = document.getElementById('vehicle-type');
                const brandSelect = document.getElementById('vehicle-brand');
                const modelSelect = document.getElementById('vehicle-model');
                const yearSelect = document.getElementById('vehicle-year');

                const searchParams = new URLSearchParams();

                if (typeSelect && typeSelect.value) searchParams.append('vehicle_type', typeSelect.value);
                if (brandSelect && brandSelect.value) searchParams.append('vehicle_brand', brandSelect.value);
                if (modelSelect && modelSelect.value) searchParams.append('vehicle_model', modelSelect.value);
                if (yearSelect && yearSelect.value) searchParams.append('vehicle_year', yearSelect.value);

                // Redirect to mobile products page with vehicle filters
                const url = `${APP_CONFIG.baseUrl}/mobile-products.html?${searchParams.toString()}`;
                console.log('Redirecting to mobile products:', url);
                window.location.href = url;
            }

            // Cart methods
            async addToCart(productId, event) {
                event.stopPropagation();

                try {
                    // Generate or get cart ID
                    let cartId = localStorage.getItem('cart_id') || 'guest_' + Date.now();
                    localStorage.setItem('cart_id', cartId);

                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/cart`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        },
                        body: JSON.stringify({
                            id: productId,
                            qty: 1,
                            cart_id: cartId
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.updateCartCount();
                        this.showToast('تم إضافة المنتج للسلة', 'success');
                    } else {
                        throw new Error('Failed to add to cart');
                    }

                } catch (error) {
                    console.error('Error adding to cart:', error);
                    this.showToast('حدث خطأ في إضافة المنتج', 'error');
                }
            }

            async loadWishlist() {
                try {
                    // Get wishlist ID from localStorage or generate one
                    let wishlistId = localStorage.getItem('wishlist_id');
                    if (!wishlistId) {
                        wishlistId = this.generateWishlistId();
                        localStorage.setItem('wishlist_id', wishlistId);
                    }

                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/wishlist/${wishlistId}`);
                    const data = await response.json();

                    if (!data.error && data.data && data.data.items) {
                        this.wishlistItems = new Set(data.data.items.map(item => item.id));
                        this.updateWishlistUI();
                    }
                } catch (error) {
                    console.error('Error loading wishlist:', error);
                }
            }

            generateWishlistId() {
                return 'wishlist_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            }

            updateWishlistUI() {
                document.querySelectorAll('.btn-wishlist').forEach(btn => {
                    const productId = parseInt(btn.dataset.productId);
                    if (this.wishlistItems.has(productId)) {
                        btn.classList.add('active');
                        btn.innerHTML = '❤️';
                    } else {
                        btn.classList.remove('active');
                        btn.innerHTML = '🤍';
                    }
                });
            }

            async toggleWishlist(productId, event) {
                event.stopPropagation();

                const btn = event.target;
                const isInWishlist = this.wishlistItems.has(productId);

                try {
                    let wishlistId = localStorage.getItem('wishlist_id');
                    if (!wishlistId) {
                        wishlistId = this.generateWishlistId();
                        localStorage.setItem('wishlist_id', wishlistId);
                    }

                    if (isInWishlist) {
                        // Remove from wishlist
                        const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/wishlist/${wishlistId}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                            },
                            body: JSON.stringify({
                                product_id: productId.toString()
                            })
                        });

                        if (response.ok) {
                            this.wishlistItems.delete(productId);
                            btn.classList.remove('active');
                            btn.innerHTML = '🤍';
                            this.showToast('تم إزالة المنتج من المفضلة', 'info');
                        }
                    } else {
                        // Add to wishlist
                        const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/wishlist`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        });

                        if (response.ok) {
                            this.wishlistItems.add(productId);
                            btn.classList.add('active');
                            btn.innerHTML = '❤️';
                            this.showToast('تم إضافة المنتج للمفضلة', 'success');
                        }
                    }

                    // Haptic feedback
                    if (window.advancedFeatures) {
                        window.advancedFeatures.hapticFeedback('light');
                    }

                } catch (error) {
                    console.error('Error toggling wishlist:', error);
                    this.showToast('حدث خطأ في تحديث المفضلة', 'error');
                }
            }

            async updateCartCount() {
                try {
                    // Try to get cart count from localStorage first
                    const localCart = JSON.parse(localStorage.getItem('cart_items') || '[]');
                    const localCount = localCart.length;

                    // Update UI with local count
                    this.cartCount = localCount;
                    this.updateCartCountUI(localCount);

                    // Try to sync with server if available
                    try {
                        let cartId = localStorage.getItem('cart_id') || 'guest_' + Date.now();
                        localStorage.setItem('cart_id', cartId);

                        const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/cart/${cartId}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            const serverCount = data.data?.count || 0;
                            this.cartCount = serverCount;
                            this.updateCartCountUI(serverCount);
                        }
                    } catch (serverError) {
                        // Server not available, use local count
                        console.log('Using local cart count:', localCount);
                    }
                } catch (error) {
                    console.error('Error updating cart count:', error);
                    // Fallback to 0
                    this.updateCartCountUI(0);
                }
            }

            updateCartCountUI(count) {
                // Update all cart count displays
                const cartCountEl = document.getElementById('cart-count');
                const navCartCountEl = document.getElementById('nav-cart-count');

                if (cartCountEl) {
                    cartCountEl.textContent = count;
                    cartCountEl.style.display = count > 0 ? 'flex' : 'none';
                }

                if (navCartCountEl) {
                    navCartCountEl.textContent = count;
                    navCartCountEl.style.display = count > 0 ? 'flex' : 'none';
                }
            }

            // Utility methods
            showLoading(show) {
                this.isLoading = show;
                // Add loading indicator logic here
            }

            showToast(message, type = 'info') {
                // Create toast notification
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.textContent = message;
                toast.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#0C55AA'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    z-index: 10000;
                    transform: translateX(400px);
                    transition: transform 0.3s ease;
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 100);
                
                setTimeout(() => {
                    toast.style.transform = 'translateX(400px)';
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }

            showError(message) {
                this.showToast(message, 'error');
            }

            showNotifications() {
                // For now, just show a simple alert
                // In a real app, this would open a notifications panel
                this.showToast('لا توجد إشعارات جديدة', 'info');
            }

            // تحميل أحسن المنتجات (الأكثر مبيعاً)
            async loadBestSellingProducts() {
                try {
                    // تحميل المنتجات الأكثر مبيعاً
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?limit=15&sort_by=sales&order_by=desc`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (!data.error && data.data && data.data.length > 0) {
                        const processedProducts = this.processProductsData(data.data);
                        this.renderProducts(processedProducts, 'best-selling-products');
                        console.log(`✅ Loaded ${processedProducts.length} best selling products`);
                    } else {
                        // إخفاء القسم إذا لم توجد منتجات
                        const bestSellingSection = document.querySelector('.best-selling-section');
                        if (bestSellingSection) {
                            bestSellingSection.style.display = 'none';
                        }
                    }
                } catch (error) {
                    console.error('Error loading best selling products:', error);
                    // إخفاء القسم في حالة الخطأ
                    const bestSellingSection = document.querySelector('.best-selling-section');
                    if (bestSellingSection) {
                        bestSellingSection.style.display = 'none';
                    }
                }
            }

            // تحميل معاينة جميع المنتجات (أول 4 منتجات)
            async loadAllProductsPreview() {
                try {
                    // تحميل أول 4 منتجات مع معلومات الصفحات
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?page=1&limit=4`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (!data.error && data.data && data.data.length > 0) {
                        const processedProducts = this.processProductsData(data.data);
                        this.renderProductsGrid(processedProducts, 'all-products-grid');

                        // تحديث معلومات المنتجات
                        this.updateProductsInfo(data);

                        // إظهار زر مشاهدة المزيد
                        document.getElementById('view-more-container').style.display = 'block';

                        console.log(`✅ Loaded ${processedProducts.length} products preview`);
                    } else {
                        // إخفاء القسم إذا لم توجد منتجات
                        const allProductsSection = document.querySelector('.all-products-section');
                        if (allProductsSection) {
                            allProductsSection.style.display = 'none';
                        }
                    }
                } catch (error) {
                    console.error('Error loading all products preview:', error);
                    // إخفاء القسم في حالة الخطأ
                    const allProductsSection = document.querySelector('.all-products-section');
                    if (allProductsSection) {
                        allProductsSection.style.display = 'none';
                    }
                }
            }

            updateProductsInfo(data) {
                const productsInfoEl = document.getElementById('products-info');
                const paginationInfoEl = document.getElementById('pagination-info');

                if (productsInfoEl && data.meta) {
                    const total = data.meta.total || 0;
                    productsInfoEl.innerHTML = `<span>${total} منتج</span>`;
                }

                if (paginationInfoEl && data.meta) {
                    const currentPage = data.meta.current_page || 1;
                    const lastPage = data.meta.last_page || 1;
                    paginationInfoEl.innerHTML = `<span>صفحة ${currentPage} من ${lastPage}</span>`;
                }
            }

            renderProductsGrid(products, containerId) {
                const container = document.getElementById(containerId);
                if (!container) return;

                container.innerHTML = '';

                products.forEach(product => {
                    const productCard = this.createProductCard(product);
                    container.appendChild(productCard);
                });
            }

            // فتح صفحة جميع المنتجات
            showAllProductsPage() {
                // إنشاء صفحة منتجات منفصلة أو modal
                this.openProductsModal();
            }

            createProductCard(product) {
                const card = document.createElement('div');
                card.className = 'product-card';

                card.innerHTML = `
                    <div class="product-image">
                        <img src="${product.image || '/vendor/core/core/base/images/placeholder.png'}"
                             alt="${product.name}"
                             loading="lazy">
                        ${product.original_price > product.price ? `<div class="discount-badge">-${Math.round(((product.original_price - product.price) / product.original_price) * 100)}%</div>` : ''}
                    </div>
                    <div class="product-info">
                        <h4 class="product-name">${product.name}</h4>
                        <div class="product-price">
                            ${this.renderProductPrice(product)}
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart-btn" onclick="app.addToCart(${product.id}, event)">
                                <span>إضافة للسلة</span>
                            </button>
                        </div>
                    </div>
                `;

                // إضافة مستمع الأحداث للنقر على الكارت
                card.addEventListener('click', (e) => {
                    if (!e.target.closest('.product-actions')) {
                        e.preventDefault();
                        e.stopPropagation();
                        this.viewProduct(product.id);
                    }
                });

                return card;
            }



            setupPWA() {
                // Register service worker
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.register('/service-worker.js')
                        .then(registration => {
                            console.log('SW registered:', registration);
                        })
                        .catch(error => {
                            console.log('SW registration failed:', error);
                        });
                }
            }

            checkAuthStatus() {
                // Check if user is logged in
                const token = localStorage.getItem('auth_token');
                const userData = localStorage.getItem('user_data');

                if (token && userData) {
                    try {
                        const user = JSON.parse(userData);
                        this.currentUser = user;
                        this.isAuthenticated = true;
                        this.updateUserInterface();
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                        this.clearAuthData();
                    }
                } else {
                    this.isAuthenticated = false;
                    this.currentUser = null;
                }
            }

            updateUserInterface() {
                // This will be called when user status changes
                console.log('👤 User interface updated');
            }

            toggleUserMenu() {
                const userMenu = document.getElementById('user-menu-dropdown');

                if (!userMenu) {
                    this.createUserMenu();
                    return;
                }

                if (userMenu.style.display === 'none' || !userMenu.style.display) {
                    this.showUserMenu();
                } else {
                    this.hideUserMenu();
                }
            }

            createUserMenu() {
                // Remove existing menu if any
                const existingMenu = document.getElementById('user-menu-dropdown');
                if (existingMenu) {
                    existingMenu.remove();
                }

                // Create new menu
                const userMenu = document.createElement('div');
                userMenu.id = 'user-menu-dropdown';
                userMenu.className = 'user-menu-dropdown';
                userMenu.style.display = 'none';

                if (this.isAuthenticated && this.currentUser) {
                    // Authenticated user menu
                    userMenu.innerHTML = `
                        <div class="user-menu-content">
                            <div class="user-menu-header">
                                <div class="user-avatar">👤</div>
                                <div class="user-info">
                                    <div class="user-name">${this.currentUser.name}</div>
                                    <div class="user-status">${this.currentUser.is_wholesale ? 'عميل جملة' : 'عميل مفرد'}</div>
                                </div>
                            </div>
                            <div class="user-menu-links">
                                <a href="#" class="user-menu-link" onclick="window.mobileApp.showUserProfile()">
                                    <span class="link-icon">👤</span>
                                    الملف الشخصي
                                </a>
                                <a href="#" class="user-menu-link" onclick="window.mobileApp.showUserOrders()">
                                    <span class="link-icon">📦</span>
                                    طلباتي
                                </a>
                                <a href="#" class="user-menu-link" onclick="window.mobileApp.showUserAddresses()">
                                    <span class="link-icon">📍</span>
                                    عناويني
                                </a>
                                <a href="#" class="user-menu-link" onclick="window.mobileApp.showUserSettings()">
                                    <span class="link-icon">⚙️</span>
                                    الإعدادات
                                </a>
                            </div>
                            <div class="user-menu-actions">
                                <button class="user-menu-btn danger" onclick="window.mobileApp.handleLogout()">
                                    <span class="btn-icon">🚪</span>
                                    تسجيل الخروج
                                </button>
                            </div>
                        </div>
                    `;
                } else {
                    // Guest user menu
                    userMenu.innerHTML = `
                        <div class="user-menu-content">
                            <div class="user-menu-header">
                                <div class="user-avatar">👤</div>
                                <div class="user-info">
                                    <div class="user-name">مرحباً بك</div>
                                    <div class="user-status">غير مسجل الدخول</div>
                                </div>
                            </div>
                            <div class="user-menu-actions">
                                <a href="mobile-login.html" class="user-menu-btn primary">
                                    <span class="btn-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
                                            <polyline points="10,17 15,12 10,7"/>
                                            <line x1="15" x2="3" y1="12" y2="12"/>
                                        </svg>
                                    </span>
                                    تسجيل الدخول
                                </a>
                                <a href="mobile-register.html" class="user-menu-btn secondary">
                                    <span class="btn-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                            <circle cx="9" cy="7" r="4"/>
                                            <line x1="19" x2="19" y1="8" y2="14"/>
                                            <line x1="22" x2="16" y1="11" y2="11"/>
                                        </svg>
                                    </span>
                                    إنشاء حساب
                                </a>
                            </div>
                        </div>
                    `;
                }

                document.body.appendChild(userMenu);
                this.showUserMenu();
            }

            showUserMenu() {
                const userMenu = document.getElementById('user-menu-dropdown');
                if (userMenu) {
                    userMenu.style.display = 'block';
                }
            }

            hideUserMenu() {
                const userMenu = document.getElementById('user-menu-dropdown');
                if (userMenu) {
                    userMenu.style.display = 'none';
                }
            }

            clearAuthData() {
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_data');
                this.isAuthenticated = false;
                this.currentUser = null;
            }

            async handleLogout() {
                try {
                    // Call logout API
                    await fetch('/customer/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        }
                    });
                } catch (error) {
                    console.error('Logout API error:', error);
                }

                // Clear local data
                this.clearAuthData();

                // Hide user menu
                this.hideUserMenu();

                // Refresh the page or redirect
                window.location.reload();
            }

            showUserProfile() {
                this.hideUserMenu();
                alert('سيتم إضافة صفحة الملف الشخصي قريباً');
            }

            showUserOrders() {
                this.hideUserMenu();
                alert('سيتم إضافة صفحة الطلبات قريباً');
            }

            showUserAddresses() {
                this.hideUserMenu();
                alert('سيتم إضافة صفحة العناوين قريباً');
            }

            showUserSettings() {
                this.hideUserMenu();
                alert('سيتم إضافة صفحة الإعدادات قريباً');
            }

            // Page methods
            showCategoriesPage() {
                this.hideAllSections();
                this.showSection('categories-full-page');
                this.loadAllCategories();
            }

            showSearchPage() {
                this.hideAllSections();
                this.showSection('search-page');
                this.focusSearchInput();
            }

            showCartPage() {
                this.hideAllSections();
                this.showSection('cart-page');
                this.loadCartItems();
            }

            showProfilePage() {
                this.hideAllSections();
                this.showSection('profile-page');
                this.loadUserProfile();
            }

            hideAllSections() {
                document.querySelectorAll('.page-section').forEach(section => {
                    section.style.display = 'none';
                });
                document.querySelector('.main-content').style.display = 'none';
            }

            showSection(sectionId) {
                const section = document.getElementById(sectionId);
                if (section) {
                    section.style.display = 'block';
                } else {
                    this.createSection(sectionId);
                }
            }

            createSection(sectionId) {
                const section = document.createElement('div');
                section.id = sectionId;
                section.className = 'page-section';
                section.style.cssText = `
                    margin-top: 70px;
                    padding: 20px 16px;
                    max-width: 500px;
                    margin-left: auto;
                    margin-right: auto;
                    min-height: calc(100vh - 150px);
                `;

                switch (sectionId) {
                    case 'categories-full-page':
                        section.innerHTML = this.getCategoriesPageHTML();
                        break;
                    case 'search-page':
                        section.innerHTML = this.getSearchPageHTML();
                        break;
                    case 'cart-page':
                        section.innerHTML = this.getCartPageHTML();
                        break;
                    case 'profile-page':
                        section.innerHTML = this.getProfilePageHTML();
                        break;
                }

                document.body.appendChild(section);
            }

            getCategoriesPageHTML() {
                return `
                    <div class="page-header">
                        <h2 style="font-size: 24px; font-weight: 700; color: #333; margin-bottom: 20px; text-align: center;">
                            📂 جميع الفئات
                        </h2>
                    </div>
                    <div class="categories-grid" id="all-categories-grid" style="
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 16px;
                    ">
                        <!-- Categories will be loaded here -->
                    </div>
                `;
            }

            getSearchPageHTML() {
                return `
                    <div class="search-page-content">
                        <div class="search-header" style="margin-bottom: 24px;">
                            <h2 style="font-size: 24px; font-weight: 700; color: #333; margin-bottom: 16px; text-align: center;">
                                🔍 البحث المتقدم
                            </h2>
                            <div class="search-container" style="margin-bottom: 20px; position: relative;">
                                <input type="text" class="search-input" placeholder="ابحث عن أي منتج..." id="advanced-search-input">
                                <button class="search-btn" id="advanced-search-btn">🔍</button>
                                <div id="search-suggestions" class="search-suggestions" style="
                                    position: absolute;
                                    top: 100%;
                                    left: 0;
                                    right: 0;
                                    background: white;
                                    border: 1px solid #ddd;
                                    border-radius: 8px;
                                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                                    max-height: 300px;
                                    overflow-y: auto;
                                    z-index: 1000;
                                    display: none;
                                "></div>
                            </div>
                        </div>

                        <div class="search-filters" style="margin-bottom: 24px;">
                            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">🎛️ تصفية النتائج</h3>
                            <div class="filter-group" style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">الفئة:</label>
                                <select id="filter-category" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                    <option value="">جميع الفئات</option>
                                </select>
                            </div>
                            <div class="filter-group" style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">العلامة التجارية:</label>
                                <select id="filter-brand" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                    <option value="">جميع العلامات التجارية</option>
                                </select>
                            </div>
                            <div class="filter-group" style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">نطاق السعر:</label>
                                <div style="display: flex; gap: 8px;">
                                    <input type="number" id="price-min" placeholder="من" style="flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 8px;">
                                    <input type="number" id="price-max" placeholder="إلى" style="flex: 1; padding: 12px; border: 1px solid #ddd; border-radius: 8px;">
                                </div>
                            </div>
                            <div class="filter-group" style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">ترتيب النتائج:</label>
                                <select id="sort-by" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                    <option value="">الترتيب الافتراضي</option>
                                    <option value="price_asc">السعر: من الأقل للأعلى</option>
                                    <option value="price_desc">السعر: من الأعلى للأقل</option>
                                    <option value="name_asc">الاسم: أ - ي</option>
                                    <option value="name_desc">الاسم: ي - أ</option>
                                    <option value="newest">الأحدث أولاً</option>
                                    <option value="popular">الأكثر شعبية</option>
                                </select>
                            </div>
                            <button id="apply-filters" style="
                                width: 100%;
                                background: #0C55AA;
                                color: white;
                                border: none;
                                padding: 14px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                            ">تطبيق الفلاتر</button>
                        </div>

                        <div class="recent-searches" style="margin-bottom: 24px;">
                            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">🕒 عمليات البحث الأخيرة</h3>
                            <div id="recent-searches-list" style="display: flex; flex-wrap: wrap; gap: 8px;">
                                <!-- Recent searches will be loaded here -->
                            </div>
                        </div>

                        <div class="popular-searches">
                            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">🔥 البحث الشائع</h3>
                            <div class="popular-tags" style="display: flex; flex-wrap: wrap; gap: 8px;">
                                <span class="search-tag" data-search="فلتر زيت">فلتر زيت</span>
                                <span class="search-tag" data-search="فرامل">فرامل</span>
                                <span class="search-tag" data-search="إطارات">إطارات</span>
                                <span class="search-tag" data-search="بطارية">بطارية</span>
                                <span class="search-tag" data-search="مصابيح">مصابيح</span>
                                <span class="search-tag" data-search="زيت محرك">زيت محرك</span>
                            </div>
                        </div>

                        <!-- Search Results Section -->
                        <div id="search-results-section" style="display: none; margin-top: 24px;">
                            <div class="results-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                <h3 style="font-size: 18px; font-weight: 600; margin: 0;">📋 نتائج البحث</h3>
                                <span id="results-count" style="color: #666; font-size: 14px;"></span>
                            </div>
                            <div id="search-results-grid" class="products-grid" style="
                                display: grid;
                                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                                gap: 16px;
                                margin-bottom: 20px;
                            ">
                                <!-- Search results will be loaded here -->
                            </div>
                            <div id="search-pagination" style="text-align: center; margin-top: 20px;">
                                <!-- Pagination will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <style>
                        .search-tag {
                            background: #f8f9fa;
                            border: 1px solid #e9ecef;
                            padding: 8px 12px;
                            border-radius: 20px;
                            font-size: 14px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        }
                        .search-tag:hover {
                            background: #0C55AA;
                            color: white;
                            border-color: #0C55AA;
                        }
                    </style>
                `;
            }

            getCartPageHTML() {
                return `
                    <div class="cart-page-content">
                        <div class="cart-header" style="margin-bottom: 24px; text-align: center;">
                            <h2 style="font-size: 24px; font-weight: 700; color: #333; margin-bottom: 8px;">
                                🛒 سلة التسوق
                            </h2>
                            <p style="color: #666; font-size: 14px;">مراجعة المنتجات قبل الشراء</p>
                        </div>

                        <div id="cart-items-container">
                            <!-- Cart items will be loaded here -->
                        </div>

                        <div class="cart-summary" id="cart-summary" style="
                            background: white;
                            border-radius: 15px;
                            padding: 20px;
                            margin-top: 20px;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
                            position: sticky;
                            bottom: 90px;
                        ">
                            <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">$0.00</span>
                            </div>
                            <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                <span>الشحن:</span>
                                <span id="shipping">مجاني</span>
                            </div>
                            <hr style="margin: 16px 0; border: none; border-top: 1px solid #eee;">
                            <div class="summary-row" style="display: flex; justify-content: space-between; font-weight: 700; font-size: 18px; color: #0C55AA;">
                                <span>المجموع الكلي:</span>
                                <span id="total">$0.00</span>
                            </div>
                            <button id="checkout-btn" style="
                                width: 100%;
                                background: #0C55AA;
                                color: white;
                                border: none;
                                padding: 16px;
                                border-radius: 12px;
                                font-size: 16px;
                                font-weight: 700;
                                margin-top: 16px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">
                                💳 متابعة الدفع
                            </button>
                        </div>
                    </div>
                `;
            }

            getProfilePageHTML() {
                return `
                    <div class="profile-page-content">
                        <div class="profile-header" style="
                            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
                            border-radius: 20px;
                            padding: 24px;
                            color: white;
                            text-align: center;
                            margin-bottom: 24px;
                        ">
                            <div class="profile-avatar" style="
                                width: 80px;
                                height: 80px;
                                background: rgba(255, 255, 255, 0.2);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin: 0 auto 16px;
                                font-size: 36px;
                            ">👤</div>
                            <h2 id="user-name" style="font-size: 20px; font-weight: 700; margin-bottom: 8px;">مرحباً بك</h2>
                            <p id="user-email" style="opacity: 0.9; font-size: 14px;"><EMAIL></p>
                        </div>

                        <div class="profile-menu">
                            <div class="menu-item" onclick="app.navigateToOrders()" style="
                                background: white;
                                border-radius: 12px;
                                padding: 16px;
                                margin-bottom: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                cursor: pointer;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                transition: all 0.3s ease;
                            ">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="font-size: 20px;">📦</span>
                                    <span style="font-weight: 600;">طلباتي</span>
                                </div>
                                <span style="color: #999;">←</span>
                            </div>

                            <div class="menu-item" onclick="app.navigateToWishlist()" style="
                                background: white;
                                border-radius: 12px;
                                padding: 16px;
                                margin-bottom: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                cursor: pointer;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                transition: all 0.3s ease;
                            ">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="font-size: 20px;">❤️</span>
                                    <span style="font-weight: 600;">المفضلة</span>
                                </div>
                                <span style="color: #999;">←</span>
                            </div>

                            <div class="menu-item" onclick="app.navigateToAddresses()" style="
                                background: white;
                                border-radius: 12px;
                                padding: 16px;
                                margin-bottom: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                cursor: pointer;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                transition: all 0.3s ease;
                            ">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="font-size: 20px;">📍</span>
                                    <span style="font-weight: 600;">عناويني</span>
                                </div>
                                <span style="color: #999;">←</span>
                            </div>

                            <div class="menu-item" onclick="app.navigateToSettings()" style="
                                background: white;
                                border-radius: 12px;
                                padding: 16px;
                                margin-bottom: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                cursor: pointer;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                transition: all 0.3s ease;
                            ">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="font-size: 20px;">⚙️</span>
                                    <span style="font-weight: 600;">الإعدادات</span>
                                </div>
                                <span style="color: #999;">←</span>
                            </div>

                            <div class="menu-item" onclick="app.logout()" style="
                                background: white;
                                border-radius: 12px;
                                padding: 16px;
                                margin-bottom: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                cursor: pointer;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                                transition: all 0.3s ease;
                                color: #dc3545;
                            ">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <span style="font-size: 20px;">🚪</span>
                                    <span style="font-weight: 600;">تسجيل الخروج</span>
                                </div>
                                <span style="color: #999;">←</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            viewProduct(productId) {
                console.log('viewProduct called with ID:', productId);
                const url = `mobile-product-detail.html?id=${productId}`;
                console.log('Navigating to:', url);
                window.location.href = url;
            }

            viewCategory(categoryId, categorySlug) {
                // Use slug if available, otherwise use ID
                const identifier = categorySlug || categoryId;
                window.location.href = `${APP_CONFIG.baseUrl}/product-categories/${identifier}`;
            }

            formatPrice(price) {
                if (!price) return '0 IQD';

                // Convert to number and format with thousands separator
                const numPrice = parseFloat(price);
                const formattedPrice = numPrice.toLocaleString('ar-IQ', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                });

                return `${formattedPrice} IQD`;
            }

            toggleWishlist(productId, event) {
                event.stopPropagation();
                console.log('Toggle wishlist for product:', productId);
            }

            toggleMenu() {
                console.log('Toggle menu');
            }

            // Additional methods for new functionality
            async loadAllCategories() {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/categories`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    this.renderAllCategories(data.data || []);
                } catch (error) {
                    console.error('Error loading all categories:', error);
                }
            }

            renderAllCategories(categories) {
                const container = document.getElementById('all-categories-grid');
                if (!container) return;

                container.innerHTML = '';

                categories.forEach(category => {
                    const item = document.createElement('a');
                    item.className = 'category-card';
                    item.href = '#';
                    item.dataset.categoryId = category.id;
                    item.style.cssText = `
                        background: white;
                        border-radius: 15px;
                        padding: 20px;
                        text-align: center;
                        text-decoration: none;
                        color: #333;
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
                        transition: all 0.3s ease;
                        display: block;
                    `;

                    item.innerHTML = `
                        <div style="
                            width: 60px;
                            height: 60px;
                            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
                            color: white;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 12px;
                            font-size: 28px;
                        ">${category.icon || '📦'}</div>
                        <div style="font-size: 14px; font-weight: 600; line-height: 1.3;">${category.name}</div>
                        <div style="font-size: 12px; color: #666; margin-top: 4px;">${category.products_count || 0} منتج</div>
                    `;

                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.viewCategory(category.id);
                    });

                    item.addEventListener('mouseenter', () => {
                        item.style.transform = 'translateY(-4px)';
                        item.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                    });

                    item.addEventListener('mouseleave', () => {
                        item.style.transform = 'translateY(0)';
                        item.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.08)';
                    });

                    container.appendChild(item);
                });
            }

            focusSearchInput() {
                setTimeout(() => {
                    const input = document.getElementById('advanced-search-input');
                    if (input) {
                        input.focus();
                        this.setupAdvancedSearch();
                    }
                }, 300);
            }

            setupAdvancedSearch() {
                const searchInput = document.getElementById('advanced-search-input');
                const searchBtn = document.getElementById('advanced-search-btn');
                const applyFiltersBtn = document.getElementById('apply-filters');

                if (searchInput) {
                    // البحث الفوري مع الاقتراحات
                    let searchTimeout;
                    searchInput.addEventListener('input', (e) => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            this.showSearchSuggestions(e.target.value);
                        }, 300);
                    });

                    // البحث عند الضغط على Enter
                    searchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.performAdvancedSearch();
                        }
                    });
                }

                if (searchBtn) {
                    searchBtn.addEventListener('click', () => {
                        this.performAdvancedSearch();
                    });
                }

                if (applyFiltersBtn) {
                    applyFiltersBtn.addEventListener('click', () => {
                        this.performAdvancedSearch();
                    });
                }

                // تحميل البيانات للفلاتر
                this.loadSearchFiltersData();
            }

            async showSearchSuggestions(query) {
                if (!query || query.length < 2) {
                    document.getElementById('search-suggestions').style.display = 'none';
                    return;
                }

                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/products?search=${encodeURIComponent(query)}&limit=5`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    const suggestions = data.data || [];
                    this.renderSearchSuggestions(suggestions, query);
                } catch (error) {
                    console.error('Error loading search suggestions:', error);
                }
            }

            renderSearchSuggestions(suggestions, query) {
                const suggestionsContainer = document.getElementById('search-suggestions');

                if (suggestions.length === 0) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }

                let html = '';
                suggestions.forEach(product => {
                    const imageUrl = this.validateImagePath(product.image);
                    html += `
                        <div class="suggestion-item" style="
                            display: flex;
                            align-items: center;
                            padding: 12px;
                            border-bottom: 1px solid #eee;
                            cursor: pointer;
                            transition: background 0.2s;
                        " onclick="app.selectSuggestion('${product.name}')">
                            <img src="${imageUrl}" alt="${product.name}" style="
                                width: 40px;
                                height: 40px;
                                object-fit: cover;
                                border-radius: 6px;
                                margin-left: 12px;
                            ">
                            <div>
                                <div style="font-weight: 600; font-size: 14px; margin-bottom: 4px;">${product.name}</div>
                                <div style="color: #0C55AA; font-size: 12px;">${this.formatPrice(product.sale_price || product.price)}</div>
                            </div>
                        </div>
                    `;
                });

                // إضافة خيار "عرض جميع النتائج"
                html += `
                    <div class="suggestion-item" style="
                        display: flex;
                        align-items: center;
                        padding: 12px;
                        cursor: pointer;
                        background: #f8f9fa;
                        font-weight: 600;
                        color: #0C55AA;
                    " onclick="app.performAdvancedSearch()">
                        🔍 عرض جميع النتائج لـ "${query}"
                    </div>
                `;

                suggestionsContainer.innerHTML = html;
                suggestionsContainer.style.display = 'block';
            }

            selectSuggestion(productName) {
                document.getElementById('advanced-search-input').value = productName;
                document.getElementById('search-suggestions').style.display = 'none';
                this.performAdvancedSearch();
            }

            async performAdvancedSearch() {
                const query = document.getElementById('advanced-search-input')?.value.trim();
                const category = document.getElementById('filter-category')?.value;
                const brand = document.getElementById('filter-brand')?.value;
                const priceMin = document.getElementById('price-min')?.value;
                const priceMax = document.getElementById('price-max')?.value;
                const sortBy = document.getElementById('sort-by')?.value;

                // إخفاء الاقتراحات
                document.getElementById('search-suggestions').style.display = 'none';

                // بناء URL البحث
                let searchUrl = `${APP_CONFIG.apiBase}/products?`;
                const params = [];

                if (query) params.push(`search=${encodeURIComponent(query)}`);
                if (category) params.push(`category=${category}`);
                if (brand) params.push(`brand=${brand}`);
                if (priceMin) params.push(`price_min=${priceMin}`);
                if (priceMax) params.push(`price_max=${priceMax}`);
                if (sortBy) params.push(`sort_by=${sortBy}`);

                params.push('limit=20'); // عدد النتائج لكل صفحة

                searchUrl += params.join('&');

                try {
                    this.showLoading(true, 'جاري البحث...');

                    const response = await fetch(searchUrl);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    const products = data.data || [];
                    this.renderSearchResults(products, query);

                    // حفظ البحث في التاريخ
                    if (query) {
                        this.saveRecentSearch(query);
                    }

                } catch (error) {
                    console.error('Error performing advanced search:', error);
                    this.showError('حدث خطأ في البحث. يرجى المحاولة مرة أخرى.');
                } finally {
                    this.showLoading(false);
                }
            }

            renderSearchResults(products, query) {
                const resultsSection = document.getElementById('search-results-section');
                const resultsGrid = document.getElementById('search-results-grid');
                const resultsCount = document.getElementById('results-count');

                // عرض قسم النتائج
                resultsSection.style.display = 'block';

                // عرض عدد النتائج
                resultsCount.textContent = `تم العثور على ${products.length} منتج`;

                if (products.length === 0) {
                    resultsGrid.innerHTML = `
                        <div style="
                            grid-column: 1 / -1;
                            text-align: center;
                            padding: 40px 20px;
                            color: #666;
                        ">
                            <div style="font-size: 48px; margin-bottom: 16px;">🔍</div>
                            <h3 style="margin-bottom: 8px;">لم يتم العثور على نتائج</h3>
                            <p>جرب البحث بكلمات مختلفة أو قم بتعديل الفلاتر</p>
                        </div>
                    `;
                    return;
                }

                // عرض النتائج
                resultsGrid.innerHTML = '';
                products.forEach(product => {
                    const productCard = this.createProductCard(product);
                    resultsGrid.appendChild(productCard);
                });

                // التمرير لقسم النتائج
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }

            async loadSearchFiltersData() {
                try {
                    // تحميل الفئات
                    const categoriesResponse = await fetch(`${APP_CONFIG.apiBase}/categories`);
                    const categoriesData = await categoriesResponse.json();

                    if (!categoriesData.error) {
                        const categorySelect = document.getElementById('filter-category');
                        if (categorySelect) {
                            categorySelect.innerHTML = '<option value="">جميع الفئات</option>';
                            (categoriesData.data || []).forEach(category => {
                                categorySelect.innerHTML += `<option value="${category.id}">${category.name}</option>`;
                            });
                        }
                    }

                    // تحميل العلامات التجارية (إذا كانت متاحة)
                    try {
                        const brandsResponse = await fetch(`${APP_CONFIG.apiBase}/brands`);
                        const brandsData = await brandsResponse.json();

                        if (!brandsData.error) {
                            const brandSelect = document.getElementById('filter-brand');
                            if (brandSelect) {
                                brandSelect.innerHTML = '<option value="">جميع العلامات التجارية</option>';
                                (brandsData.data || []).forEach(brand => {
                                    brandSelect.innerHTML += `<option value="${brand.id}">${brand.name}</option>`;
                                });
                            }
                        }
                    } catch (error) {
                        console.log('Brands API not available:', error);
                    }

                } catch (error) {
                    console.error('Error loading search filters data:', error);
                }
            }

            saveRecentSearch(query) {
                let recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');

                // إزالة البحث إذا كان موجود مسبقاً
                recentSearches = recentSearches.filter(search => search !== query);

                // إضافة البحث في المقدمة
                recentSearches.unshift(query);

                // الاحتفاظ بآخر 10 عمليات بحث فقط
                recentSearches = recentSearches.slice(0, 10);

                localStorage.setItem('recentSearches', JSON.stringify(recentSearches));

                // تحديث عرض البحث الأخير
                this.updateRecentSearchesDisplay();
            }

            updateRecentSearchesDisplay() {
                const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
                const recentSearchesList = document.getElementById('recent-searches-list');

                if (recentSearchesList && recentSearches.length > 0) {
                    recentSearchesList.innerHTML = '';
                    recentSearches.forEach(search => {
                        const searchTag = document.createElement('span');
                        searchTag.className = 'search-tag';
                        searchTag.textContent = search;
                        searchTag.style.cursor = 'pointer';
                        searchTag.addEventListener('click', () => {
                            document.getElementById('advanced-search-input').value = search;
                            this.performAdvancedSearch();
                        });
                        recentSearchesList.appendChild(searchTag);
                    });
                }
            }

            async loadCartItems() {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/cart`);
                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    this.renderCartItems(data.data || []);
                } catch (error) {
                    console.error('Error loading cart items:', error);
                    this.showError('حدث خطأ في تحميل السلة');
                }
            }

            renderCartItems(cartData) {
                const container = document.getElementById('cart-items-container');
                if (!container) return;

                const items = cartData.items || [];

                if (items.length === 0) {
                    container.innerHTML = `
                        <div style="
                            text-align: center;
                            padding: 60px 20px;
                            color: #666;
                        ">
                            <div style="font-size: 60px; margin-bottom: 16px;">🛒</div>
                            <h3 style="font-size: 18px; margin-bottom: 8px;">السلة فارغة</h3>
                            <p style="font-size: 14px; margin-bottom: 20px;">ابدأ بإضافة المنتجات لسلة التسوق</p>
                            <button onclick="app.navigateTo('home')" style="
                                background: #0C55AA;
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                            ">تصفح المنتجات</button>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = '';
                let subtotal = 0;

                items.forEach(item => {
                    const itemTotal = item.price * item.qty;
                    subtotal += itemTotal;

                    const cartItem = document.createElement('div');
                    cartItem.className = 'cart-item';
                    cartItem.style.cssText = `
                        background: white;
                        border-radius: 15px;
                        padding: 16px;
                        margin-bottom: 12px;
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
                        display: flex;
                        gap: 12px;
                    `;

                    cartItem.innerHTML = `
                        <div style="
                            width: 80px;
                            height: 80px;
                            background: #f8f9fa;
                            border-radius: 8px;
                            overflow: hidden;
                            flex-shrink: 0;
                        ">
                            <img src="${item.image || '/storage/main/general/placeholder.png'}"
                                 alt="${item.name}"
                                 style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div style="flex: 1;">
                            <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 6px; line-height: 1.3;">
                                ${item.name}
                            </h4>
                            <div style="font-size: 16px; font-weight: 700; color: #0C55AA; margin-bottom: 8px;">
                                $${item.price}
                            </div>
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center; background: #f8f9fa; border-radius: 8px; overflow: hidden;">
                                    <button onclick="app.updateCartQuantity(${item.id}, ${item.qty - 1})" style="
                                        width: 32px;
                                        height: 32px;
                                        border: none;
                                        background: transparent;
                                        cursor: pointer;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-weight: 600;
                                    ">-</button>
                                    <span style="
                                        width: 40px;
                                        text-align: center;
                                        font-weight: 600;
                                        padding: 6px 0;
                                    ">${item.qty}</span>
                                    <button onclick="app.updateCartQuantity(${item.id}, ${item.qty + 1})" style="
                                        width: 32px;
                                        height: 32px;
                                        border: none;
                                        background: transparent;
                                        cursor: pointer;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-weight: 600;
                                    ">+</button>
                                </div>
                                <button onclick="app.removeFromCart(${item.id})" style="
                                    color: #dc3545;
                                    background: none;
                                    border: none;
                                    font-size: 18px;
                                    cursor: pointer;
                                    padding: 4px;
                                ">🗑️</button>
                            </div>
                        </div>
                    `;

                    container.appendChild(cartItem);
                });

                // Update summary
                this.updateCartSummary(subtotal);
            }

            updateCartSummary(subtotal) {
                const subtotalEl = document.getElementById('subtotal');
                const totalEl = document.getElementById('total');

                if (subtotalEl) subtotalEl.textContent = `$${subtotal.toFixed(2)}`;
                if (totalEl) totalEl.textContent = `$${subtotal.toFixed(2)}`;
            }

            async updateCartQuantity(itemId, newQty) {
                if (newQty < 1) {
                    this.removeFromCart(itemId);
                    return;
                }

                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/cart/update`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        },
                        body: JSON.stringify({
                            id: itemId,
                            qty: newQty
                        })
                    });

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    this.loadCartItems();
                    this.updateCartCount();

                } catch (error) {
                    console.error('Error updating cart:', error);
                    this.showError('حدث خطأ في تحديث السلة');
                }
            }

            async removeFromCart(itemId) {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/cart/remove`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        },
                        body: JSON.stringify({
                            id: itemId
                        })
                    });

                    const data = await response.json();

                    if (data.error) {
                        throw new Error(data.message);
                    }

                    this.loadCartItems();
                    this.updateCartCount();
                    this.showToast('تم حذف المنتج من السلة', 'success');

                } catch (error) {
                    console.error('Error removing from cart:', error);
                    this.showError('حدث خطأ في حذف المنتج');
                }
            }

            async loadUserProfile() {
                try {
                    const response = await fetch(`${APP_CONFIG.apiBase}/customer/profile`);
                    const data = await response.json();

                    if (data.error) {
                        // User not logged in
                        this.showLoginPrompt();
                        return;
                    }

                    this.renderUserProfile(data.data);
                } catch (error) {
                    console.error('Error loading profile:', error);
                    this.showLoginPrompt();
                }
            }

            renderUserProfile(user) {
                const nameEl = document.getElementById('user-name');
                const emailEl = document.getElementById('user-email');

                if (nameEl) nameEl.textContent = user.name || 'مستخدم';
                if (emailEl) emailEl.textContent = user.email || '';
            }

            showLoginPrompt() {
                const profileSection = document.getElementById('profile-page');
                if (profileSection) {
                    profileSection.innerHTML = `
                        <div style="
                            text-align: center;
                            padding: 60px 20px;
                            color: #666;
                        ">
                            <div style="font-size: 60px; margin-bottom: 16px;">👤</div>
                            <h3 style="font-size: 18px; margin-bottom: 8px;">تسجيل الدخول مطلوب</h3>
                            <p style="font-size: 14px; margin-bottom: 20px;">سجل دخولك للوصول لحسابك</p>
                            <button onclick="app.navigateToLogin()" style="
                                background: #0C55AA;
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                                margin-right: 8px;
                            ">تسجيل الدخول</button>
                            <button onclick="app.navigateToRegister()" style="
                                background: transparent;
                                color: #0C55AA;
                                border: 2px solid #0C55AA;
                                padding: 10px 22px;
                                border-radius: 8px;
                                font-weight: 600;
                                cursor: pointer;
                            ">إنشاء حساب</button>
                        </div>
                    `;
                }
            }

            // Navigation methods for profile menu
            navigateToOrders() {
                window.location.href = `${APP_CONFIG.baseUrl}/customer/orders`;
            }

            navigateToWishlist() {
                window.location.href = `${APP_CONFIG.baseUrl}/wishlist`;
            }

            navigateToAddresses() {
                window.location.href = `${APP_CONFIG.baseUrl}/customer/addresses`;
            }

            navigateToSettings() {
                window.location.href = `${APP_CONFIG.baseUrl}/customer/edit-account`;
            }

            navigateToLogin() {
                window.location.href = 'mobile-login.html';
            }

            navigateToRegister() {
                window.location.href = 'mobile-register.html';
            }

            async logout() {
                if (confirm('هل تريد تسجيل الخروج؟')) {
                    try {
                        await fetch(`${APP_CONFIG.baseUrl}/logout`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                            }
                        });

                        window.location.reload();
                    } catch (error) {
                        console.error('Logout error:', error);
                    }
                }
            }

            openProductsModal() {
                // إنشاء modal لعرض جميع المنتجات مع pagination
                const modal = document.createElement('div');
                modal.className = 'products-modal';
                modal.innerHTML = `
                    <div class="modal-overlay" onclick="app.closeProductsModal()"></div>
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>جميع المنتجات</h3>
                            <button class="close-btn" onclick="app.closeProductsModal()">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="products-grid-modal" id="modal-products-grid">
                                <div class="loading-spinner">جاري التحميل...</div>
                            </div>
                            <div class="pagination-container" id="pagination-container">
                                <!-- Pagination will be loaded here -->
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';

                // تحميل الصفحة الأولى
                this.loadModalProducts(1);
            }

            closeProductsModal() {
                const modal = document.querySelector('.products-modal');
                if (modal) {
                    modal.remove();
                    document.body.style.overflow = 'auto';
                }
            }

            async loadModalProducts(page = 1) {
                try {
                    // إظهار حالة التحميل
                    const container = document.getElementById('modal-products-grid');
                    if (container) {
                        container.innerHTML = '<div class="loading-spinner">جاري التحميل...</div>';
                    }

                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?page=${page}&limit=12`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (!data.error && data.data && data.data.length > 0) {
                        const processedProducts = this.processProductsData(data.data);
                        this.renderModalProducts(processedProducts);
                        this.renderPagination(data.meta);

                        console.log(`✅ Loaded page ${page}: ${processedProducts.length} products`);
                    }
                } catch (error) {
                    console.error(`Error loading products page ${page}:`, error);
                    const container = document.getElementById('modal-products-grid');
                    if (container) {
                        container.innerHTML = '<div class="error-message">حدث خطأ في تحميل المنتجات</div>';
                    }
                }
            }

            renderModalProducts(products) {
                const container = document.getElementById('modal-products-grid');
                if (!container) return;

                container.innerHTML = '';

                products.forEach(product => {
                    // إنشاء كرت منتج مثل "أحسن المنتجات" تماماً
                    const card = document.createElement('a');
                    card.className = 'product-card';
                    card.href = '#';
                    card.dataset.productId = product.id;
                    card.onclick = () => this.viewProduct(product.id);

                    const discount = product.original_price > product.price && product.price > 0 ?
                        Math.round((1 - product.price / product.original_price) * 100) : 0;

                    card.innerHTML = `
                        <div class="product-img">
                            <img src="${product.image || '/storage/main/general/placeholder.png'}"
                                 alt="${product.name}" loading="lazy"
                                 onerror="console.log('❌ Image failed:', this.src); this.onerror=null; this.src='/storage/main/general/placeholder.png';"
                                 onload="console.log('✅ Image loaded successfully:', this.src)"
                            ${discount > 0 ? `<div class="product-badge">-${discount}%</div>` : ''}
                        </div>
                        <div class="product-info">
                            <h4 class="product-name">${product.name}</h4>
                            <div class="product-price">
                                ${this.renderProductPrice(product)}
                            </div>
                            <div class="product-actions">
                                <button class="add-to-cart-btn" onclick="app.addToCart(${product.id}, event)">
                                    <span>إضافة للسلة</span>
                                </button>
                                <button class="wishlist-btn ${this.isInWishlist(product.id) ? 'active' : ''}"
                                        onclick="app.toggleWishlist(${product.id}, event)">
                                    <i class="wishlist-icon">${this.isInWishlist(product.id) ? '♥' : '♡'}</i>
                                </button>
                            </div>
                        </div>
                    `;

                    container.appendChild(card);
                });

                console.log(`✅ Rendered ${products.length} products in modal`);
            }

            renderPagination(meta) {
                const container = document.getElementById('pagination-container');
                if (!container || !meta) return;

                const currentPage = meta.current_page || 1;
                const lastPage = meta.last_page || 1;
                const total = meta.total || 0;

                let paginationHTML = `
                    <div class="pagination-info-modal">
                        <span>عرض ${meta.from || 1} - ${meta.to || 0} من ${total} منتج</span>
                    </div>
                    <div class="pagination-buttons">
                `;

                // زر الصفحة السابقة
                if (currentPage > 1) {
                    paginationHTML += `<button class="page-btn" onclick="app.loadModalProducts(${currentPage - 1})">السابق</button>`;
                }

                // أرقام الصفحات
                const startPage = Math.max(1, currentPage - 2);
                const endPage = Math.min(lastPage, currentPage + 2);

                if (startPage > 1) {
                    paginationHTML += `<button class="page-btn" onclick="app.loadModalProducts(1)">1</button>`;
                    if (startPage > 2) {
                        paginationHTML += `<span class="page-dots">...</span>`;
                    }
                }

                for (let i = startPage; i <= endPage; i++) {
                    const activeClass = i === currentPage ? 'active' : '';
                    paginationHTML += `<button class="page-btn ${activeClass}" onclick="app.loadModalProducts(${i})">${i}</button>`;
                }

                if (endPage < lastPage) {
                    if (endPage < lastPage - 1) {
                        paginationHTML += `<span class="page-dots">...</span>`;
                    }
                    paginationHTML += `<button class="page-btn" onclick="app.loadModalProducts(${lastPage})">${lastPage}</button>`;
                }

                // زر الصفحة التالية
                if (currentPage < lastPage) {
                    paginationHTML += `<button class="page-btn" onclick="app.loadModalProducts(${currentPage + 1})">التالي</button>`;
                }

                paginationHTML += `</div>`;
                container.innerHTML = paginationHTML;
            }
        }

        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.app = new MobileApp();
            window.mobileApp = window.app; // إضافة مرجع إضافي
        });
    </script>
</body>
</html>
