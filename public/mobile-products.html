<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>المنتجات - دليل قطع الغيار</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليل قطع الغيار">
    <meta name="msapplication-TileColor" content="#0C55AA">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="dummy-token-for-demo">
    
    <!-- Icons -->
    <link rel="apple-touch-icon" sizes="192x192" href="/pwa/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/pwa/icon-32x32.png">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding-bottom: 80px;
            overflow-x: hidden;
        }
        
        /* Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            padding: 10px 16px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(12, 85, 170, 0.3);
            padding-top: calc(10px + env(safe-area-inset-top));
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .back-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 18px;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 700;
            flex: 1;
            text-align: center;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .header-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        
        .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 0 16px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Search Results Header */
        .search-results-header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        
        .search-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .search-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .search-details h2 {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }
        
        .search-details p {
            font-size: 14px;
            color: #666;
        }
        
        .search-filters {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .filter-tag {
            background: #e3f2fd;
            color: #0C55AA;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        /* Products Grid */
        .products-section {
            margin: 20px 0;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
            position: relative;
            border: 1px solid #f0f0f0;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #0C55AA;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #0C55AA, #3D73C4);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .product-card:hover::before {
            transform: scaleX(1);
        }
        
        .product-img {
            width: 100%;
            height: 140px;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
        
        .product-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .product-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 6px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
        }
        
        .price-current {
            font-size: 16px;
            font-weight: 700;
            color: #0C55AA;
        }
        
        .price-old {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
        }
        
        .product-actions {
            display: flex;
            gap: 6px;
        }
        
        .btn-cart {
            flex: 1;
            background: #0C55AA;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-cart:hover {
            background: #094488;
        }

        .btn-cart.disabled,
        .btn-cart:disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn-cart.disabled:hover,
        .btn-cart:disabled:hover {
            background: #6c757d;
            transform: none;
        }
        
        .btn-wishlist {
            width: 36px;
            height: 36px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-wishlist:hover,
        .btn-wishlist.active {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        /* Enhanced Price Styles */
        .price-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .price-label {
            font-size: 11px;
            color: #666;
            min-width: 40px;
        }

        .price-current {
            font-weight: 600;
            color: #0C55AA;
        }

        .price-old {
            text-decoration: line-through;
            color: #999;
            font-size: 12px;
        }

        .wholesale-price {
            font-weight: 600;
            color: #28a745;
        }

        .price-row.wholesale {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 6px;
            margin-top: 4px;
        }

        .wholesale-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 8px;
        }

        .wholesale-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 8px;
            margin-top: 4px;
        }

        .wholesale-info-text {
            font-size: 11px;
            color: #1976d2;
            line-height: 1.3;
        }

        .wholesale-info-text a {
            color: #1976d2;
            font-weight: 600;
            text-decoration: underline;
        }

        .price-on-request {
            color: #666;
            font-style: italic;
            font-size: 12px;
        }
        
        /* Loading States */
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0C55AA;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 60px;
            margin-bottom: 16px;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .empty-text {
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background: #0C55AA;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: #094488;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            text-decoration: none;
            color: #6c757d;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 60px;
            position: relative;
        }
        
        .nav-item:hover,
        .nav-item.active {
            color: #0C55AA;
            background: rgba(12, 85, 170, 0.1);
        }
        
        .nav-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        
        .nav-text {
            font-size: 11px;
            font-weight: 600;
        }
        
        .nav-badge {
            position: absolute;
            top: 2px;
            right: 8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        /* Responsive */
        @media (max-width: 480px) {
            .main-content {
                padding: 0 12px;
            }
            
            .products-grid {
                gap: 8px;
            }
            
            .product-info {
                padding: 10px;
            }
        }
        
        /* Animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">
                ←
            </button>
            <div class="page-title">نتائج البحث</div>
            <div class="header-actions">
                <button class="header-btn" id="cart-btn">
                    🛒
                    <span class="badge" id="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Search Results Header -->
        <section class="search-results-header fade-in" id="search-header">
            <!-- Will be populated by JavaScript -->
        </section>

        <!-- Products Section -->
        <section class="products-section fade-in">
            <div class="products-grid" id="products-container">
                <!-- Loading state -->
                <div class="loading-container" style="grid-column: 1 / -1;">
                    <div class="spinner"></div>
                    <p>جاري تحميل المنتجات...</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="nav-items">
            <a href="/mobile-app.html" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">الرئيسية</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">📂</div>
                <div class="nav-text">الفئات</div>
            </a>
            <a href="#" class="nav-item active">
                <div class="nav-icon">🔍</div>
                <div class="nav-text">البحث</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-text">السلة</div>
                <span class="nav-badge" id="nav-cart-count" style="display: none;">0</span>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-text">حسابي</div>
            </a>
        </div>
    </nav>

    <script>
        // App Configuration
        const APP_CONFIG = {
            apiBase: 'http://localhost:8000/api/v1',
            baseUrl: 'http://localhost:8000',
            locale: 'ar',
            currency: 'IQD',
            currencySymbol: 'IQD'
        };

        // Mobile Products App
        class MobileProductsApp {
            constructor() {
                this.products = [];
                this.searchParams = new URLSearchParams(window.location.search);
                this.wishlistItems = new Set();
                
                this.init();
            }

            init() {
                this.checkAuthStatus();
                this.loadCategoryInfo();
                this.loadSearchResults();
                this.updateCartCount();
                this.loadWishlist();
            }

            checkAuthStatus() {
                // Check if user is logged in
                const token = localStorage.getItem('auth_token');
                const userData = localStorage.getItem('user_data');

                if (token && userData) {
                    try {
                        const user = JSON.parse(userData);
                        this.currentUser = user;
                        this.isAuthenticated = true;
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                        this.isAuthenticated = false;
                        this.currentUser = null;
                    }
                } else {
                    this.isAuthenticated = false;
                    this.currentUser = null;
                }
            }

            async loadCategoryInfo() {
                const categoryId = this.searchParams.get('category_id');
                if (categoryId) {
                    try {
                        const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/product-categories`);
                        const data = await response.json();

                        if (!data.error && data.data) {
                            const category = data.data.find(cat => cat.id == categoryId);
                            if (category) {
                                this.categoryInfo = category;
                                console.log('Category info loaded:', category.name);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading category info:', error);
                    }
                }
            }

            async loadSearchResults() {
                try {
                    // Build search query from URL parameters
                    const searchQuery = this.buildSearchQuery();

                    // Update search header
                    this.updateSearchHeader();

                    console.log('Loading products with query:', searchQuery);

                    // Load products
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/products?${searchQuery}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        console.log('Products API response:', data);

                        this.products = data.data || [];
                        this.totalProducts = data.meta?.total || this.products.length;

                        console.log(`Loaded ${this.products.length} products (total: ${this.totalProducts})`);
                        this.renderProducts();
                    } else {
                        console.error('Products API error:', response.status, response.statusText);
                        this.showEmptyState();
                    }
                } catch (error) {
                    console.error('Error loading products:', error);
                    this.showEmptyState();
                }
            }

            buildSearchQuery() {
                const params = new URLSearchParams();

                // Handle category filtering
                const categoryId = this.searchParams.get('category_id');
                const categorySlug = this.searchParams.get('category_slug');

                if (categoryId) {
                    params.append('categories[]', categoryId);
                } else if (categorySlug) {
                    // If only slug is provided, we'll need to find the category ID
                    // For now, we'll use the slug as a search term
                    params.append('search', categorySlug);
                }

                // Add vehicle search parameters
                const vehicleType = this.searchParams.get('vehicle_type');
                const vehicleBrand = this.searchParams.get('vehicle_brand');
                const vehicleModel = this.searchParams.get('vehicle_model');
                const vehicleYear = this.searchParams.get('vehicle_year');

                if (vehicleType) params.append('vehicle_type', vehicleType);
                if (vehicleBrand) params.append('vehicle_brand', vehicleBrand);
                if (vehicleModel) params.append('vehicle_model', vehicleModel);
                if (vehicleYear) params.append('vehicle_year', vehicleYear);

                // Add other search parameters
                for (const [key, value] of this.searchParams.entries()) {
                    if (!['category_id', 'category_slug', 'vehicle_type', 'vehicle_brand', 'vehicle_model', 'vehicle_year'].includes(key)) {
                        params.append(key, value);
                    }
                }

                return params.toString();
            }

            updateSearchHeader() {
                const header = document.getElementById('search-header');
                const categoryId = this.searchParams.get('category_id');
                const categorySlug = this.searchParams.get('category_slug');
                const vehicleType = this.searchParams.get('vehicle_type');
                const vehicleBrand = this.searchParams.get('vehicle_brand');
                const vehicleModel = this.searchParams.get('vehicle_model');
                const vehicleYear = this.searchParams.get('vehicle_year');

                let searchTitle = 'نتائج البحث';
                let searchDescription = 'جميع المنتجات المتاحة';
                let filters = [];

                // Handle category search
                if (categoryId || categorySlug) {
                    const categoryName = this.categoryInfo ? this.categoryInfo.name : (categorySlug ? categorySlug.replace(/-/g, ' ') : 'غير محدد');
                    searchTitle = `منتجات ${categoryName}`;
                    searchDescription = `جميع المنتجات المتاحة في فئة ${categoryName}`;

                    filters.push(`الفئة: ${categoryName}`);
                }

                // Handle vehicle search
                if (vehicleType || vehicleBrand || vehicleModel || vehicleYear) {
                    searchTitle = 'قطع غيار السيارات';
                    searchDescription = 'قطع الغيار المناسبة لسيارتك';

                    if (vehicleType) filters.push(`النوع: ${vehicleType}`);
                    if (vehicleBrand) filters.push(`الماركة: ${vehicleBrand}`);
                    if (vehicleModel) filters.push(`الموديل: ${vehicleModel}`);
                    if (vehicleYear) filters.push(`السنة: ${vehicleYear}`);
                }

                header.innerHTML = `
                    <div class="search-info">
                        <div class="search-icon">
                            <img src="/storage/main/general/logo-white.png" alt="دليل قطع الغيار" style="width: 100%; height: 100%; object-fit: contain;">
                        </div>
                        <div class="search-details">
                            <h2>${searchTitle}</h2>
                            <p>${searchDescription}</p>
                        </div>
                    </div>
                    ${filters.length > 0 ? `
                        <div class="search-filters">
                            ${filters.map(filter => `<span class="filter-tag">${filter}</span>`).join('')}
                        </div>
                    ` : ''}
                `;
            }

            renderProducts() {
                const container = document.getElementById('products-container');
                
                if (this.products.length === 0) {
                    this.showEmptyState();
                    return;
                }
                
                container.innerHTML = '';
                
                this.products.forEach(product => {
                    const card = document.createElement('a');
                    card.className = 'product-card';
                    card.href = '#';
                    card.dataset.productId = product.id;
                    
                    const discount = product.sale_price ? 
                        Math.round(((product.price - product.sale_price) / product.price) * 100) : 0;
                    
                    card.innerHTML = `
                        <div class="product-img">
                            <img src="${product.image || '/storage/main/general/placeholder.png'}"
                                 alt="${product.name}" loading="lazy">
                            ${discount > 0 ? `<div class="product-badge">-${discount}%</div>` : ''}
                            ${product.is_featured ? `<div class="product-badge featured-badge" style="background: #28a745; top: 8px; left: 8px;">مميز</div>` : ''}
                        </div>
                        <div class="product-info">
                            <div class="product-name">${product.name}</div>
                            ${product.brand ? `<div class="product-brand" style="font-size: 12px; color: #666; margin-bottom: 4px;">الماركة: ${product.brand}</div>` : ''}
                            ${product.sku ? `<div class="product-sku" style="font-size: 11px; color: #999; margin-bottom: 6px;">كود المنتج: ${product.sku}</div>` : ''}
                            <div class="product-price">
                                ${this.renderProductPrice(product)}
                            </div>
                            ${product.quantity !== undefined ? `
                                <div class="product-stock" style="font-size: 11px; margin-bottom: 8px; display: flex; align-items: center; gap: 4px;">
                                    <span style="width: 8px; height: 8px; border-radius: 50%; background: ${product.quantity > 0 ? '#28a745' : '#dc3545'};"></span>
                                    <span style="color: ${product.quantity > 0 ? '#28a745' : '#dc3545'};">
                                        ${product.quantity > 0 ? `متوفر (${product.quantity})` : 'غير متوفر'}
                                    </span>
                                </div>
                            ` : ''}
                            <div class="product-actions">
                                <button class="btn-cart ${product.quantity === 0 ? 'disabled' : ''}"
                                        onclick="addToCart(${product.id}, event)"
                                        ${product.quantity === 0 ? 'disabled' : ''}>
                                    ${product.quantity === 0 ? 'غير متوفر' : 'إضافة للسلة'}
                                </button>
                                <button class="btn-wishlist" data-product-id="${product.id}" onclick="toggleWishlist(${product.id}, event)">
                                    🤍
                                </button>
                            </div>
                        </div>
                    `;
                    
                    card.addEventListener('click', (e) => {
                        if (!e.target.closest('.product-actions')) {
                            e.preventDefault();
                            this.viewProduct(product.id);
                        }
                    });
                    
                    container.appendChild(card);
                });
                
                console.log(`Rendered ${this.products.length} products`);
            }

            showEmptyState() {
                const container = document.getElementById('products-container');
                const categoryName = this.categoryInfo ? this.categoryInfo.name : 'الفئة المحددة';

                container.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <div class="empty-icon">📦</div>
                        <h3 class="empty-title">لا توجد منتجات</h3>
                        <p class="empty-text">لم نجد أي منتجات في ${categoryName}</p>
                        <div style="margin: 16px 0; padding: 12px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
                            جرب البحث في فئة أخرى أو تصفح جميع المنتجات
                        </div>
                        <button class="btn-primary" onclick="goBack()">العودة للفئات</button>
                        <button class="btn-primary" onclick="window.location.href='/mobile-app.html'" style="margin-left: 8px; background: #6c757d;">
                            الصفحة الرئيسية
                        </button>
                    </div>
                `;
            }

            async updateCartCount() {
                try {
                    let cartId = localStorage.getItem('cart_id') || 'guest_' + Date.now();
                    localStorage.setItem('cart_id', cartId);
                    
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/cart/${cartId}`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        const count = data.data?.count || 0;
                        
                        document.getElementById('cart-count').textContent = count;
                        document.getElementById('nav-cart-count').textContent = count;
                        
                        document.getElementById('cart-count').style.display = count > 0 ? 'flex' : 'none';
                        document.getElementById('nav-cart-count').style.display = count > 0 ? 'flex' : 'none';
                    }
                } catch (error) {
                    console.error('Error updating cart count:', error);
                }
            }

            async loadWishlist() {
                try {
                    let wishlistId = localStorage.getItem('wishlist_id');
                    if (!wishlistId) {
                        wishlistId = 'wishlist_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
                        localStorage.setItem('wishlist_id', wishlistId);
                    }
                    
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/wishlist/${wishlistId}`);
                    const data = await response.json();
                    
                    if (!data.error && data.data && data.data.items) {
                        this.wishlistItems = new Set(data.data.items.map(item => item.id));
                        this.updateWishlistUI();
                    }
                } catch (error) {
                    console.error('Error loading wishlist:', error);
                }
            }

            updateWishlistUI() {
                document.querySelectorAll('.btn-wishlist').forEach(btn => {
                    const productId = parseInt(btn.dataset.productId);
                    if (this.wishlistItems.has(productId)) {
                        btn.classList.add('active');
                        btn.innerHTML = '❤️';
                    } else {
                        btn.classList.remove('active');
                        btn.innerHTML = '🤍';
                    }
                });
            }

            viewProduct(productId) {
                window.location.href = `${APP_CONFIG.baseUrl}/products/${productId}`;
            }

            formatPrice(price) {
                if (!price) return '0 IQD';

                // Convert to number and format with thousands separator
                const numPrice = parseFloat(price);
                const formattedPrice = numPrice.toLocaleString('ar-IQ', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                });

                return `${formattedPrice} IQD`;
            }

            renderProductPrice(product) {
                if (product.price <= 0) {
                    return '<span class="price-on-request">السعر عند الطلب</span>';
                }

                let priceHtml = '';
                const isWholesaleCustomer = this.currentUser && this.currentUser.is_wholesale;

                // سعر التجزئة (المفرد)
                if (product.original_price > product.price) {
                    // يوجد خصم
                    priceHtml += `
                        <div class="price-row">
                            <span class="price-label">المفرد:</span>
                            <span class="price-current">${product.price_formatted || this.formatPrice(product.price)}</span>
                            <span class="price-old">${product.original_price_formatted || this.formatPrice(product.original_price)}</span>
                        </div>
                    `;
                } else {
                    priceHtml += `
                        <div class="price-row">
                            <span class="price-label">المفرد:</span>
                            <span class="price-current">${product.price_formatted || this.formatPrice(product.price)}</span>
                        </div>
                    `;
                }

                // سعر الجملة (إذا كان متوفراً وكان المستخدم عميل جملة)
                if (product.wholesale_price && product.wholesale_price > 0) {
                    if (isWholesaleCustomer) {
                        // عرض سعر الجملة للعملاء المعتمدين
                        priceHtml += `
                            <div class="price-row wholesale">
                                <span class="price-label">الجملة:</span>
                                <span class="wholesale-price">${product.wholesale_price_formatted || this.formatPrice(product.wholesale_price)}</span>
                                <span class="wholesale-badge">✓ معتمد</span>
                            </div>
                        `;
                    } else if (this.isAuthenticated) {
                        // عرض رسالة للعملاء المفردين المسجلين
                        priceHtml += `
                            <div class="price-row wholesale-info">
                                <span class="wholesale-info-text">💼 أسعار الجملة متاحة للتجار المعتمدين</span>
                            </div>
                        `;
                    } else {
                        // عرض رسالة للزوار غير المسجلين
                        priceHtml += `
                            <div class="price-row wholesale-info">
                                <span class="wholesale-info-text">💼 <a href="/mobile-register.html">سجل كعميل جملة</a> للحصول على أسعار خاصة</span>
                            </div>
                        `;
                    }
                }

                return priceHtml;
            }
        }

        // Global functions
        function goBack() {
            if (document.referrer && document.referrer.includes('mobile-app.html')) {
                window.history.back();
            } else {
                window.location.href = '/mobile-app.html';
            }
        }

        async function addToCart(productId, event) {
            event.stopPropagation();
            
            try {
                let cartId = localStorage.getItem('cart_id') || 'guest_' + Date.now();
                localStorage.setItem('cart_id', cartId);
                
                const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/cart`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                    },
                    body: JSON.stringify({
                        id: productId,
                        qty: 1,
                        cart_id: cartId
                    })
                });
                
                if (response.ok) {
                    window.app.updateCartCount();
                    showToast('تم إضافة المنتج للسلة', 'success');
                } else {
                    throw new Error('Failed to add to cart');
                }
                
            } catch (error) {
                console.error('Error adding to cart:', error);
                showToast('حدث خطأ في إضافة المنتج', 'error');
            }
        }

        async function toggleWishlist(productId, event) {
            event.stopPropagation();
            
            const btn = event.target;
            const isInWishlist = window.app.wishlistItems.has(productId);
            
            try {
                let wishlistId = localStorage.getItem('wishlist_id');
                if (!wishlistId) {
                    wishlistId = 'wishlist_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
                    localStorage.setItem('wishlist_id', wishlistId);
                }
                
                if (isInWishlist) {
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/wishlist/${wishlistId}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        },
                        body: JSON.stringify({
                            product_id: productId.toString()
                        })
                    });
                    
                    if (response.ok) {
                        window.app.wishlistItems.delete(productId);
                        btn.classList.remove('active');
                        btn.innerHTML = '🤍';
                        showToast('تم إزالة المنتج من المفضلة', 'info');
                    }
                } else {
                    const response = await fetch(`${APP_CONFIG.apiBase}/ecommerce/wishlist`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                        },
                        body: JSON.stringify({
                            product_id: productId
                        })
                    });
                    
                    if (response.ok) {
                        window.app.wishlistItems.add(productId);
                        btn.classList.add('active');
                        btn.innerHTML = '❤️';
                        showToast('تم إضافة المنتج للمفضلة', 'success');
                    }
                }
                
            } catch (error) {
                console.error('Error toggling wishlist:', error);
                showToast('حدث خطأ في تحديث المفضلة', 'error');
            }
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#0C55AA'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(400px)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.app = new MobileProductsApp();
        });
    </script>
</body>
</html>
