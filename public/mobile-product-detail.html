<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>تفاصيل المنتج - دليل قطع الغيار</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding-bottom: 80px;
        }
        
        /* Header */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            padding: 10px 16px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(12, 85, 170, 0.3);
            padding-top: calc(10px + env(safe-area-inset-top));
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .back-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }
        
        .back-btn svg {
            width: 20px;
            height: 20px;
        }
        
        .header-title {
            font-size: 16px;
            font-weight: 600;
            flex: 1;
            text-align: center;
            margin: 0 16px;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .header-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .header-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }
        
        .header-btn svg {
            width: 20px;
            height: 20px;
        }
        
        /* Main Content */
        .main-content {
            margin-top: 70px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Product Gallery */
        .product-gallery {
            position: relative;
            background: white;
            margin-bottom: 8px;
        }
        
        .gallery-main {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;
        }
        
        .gallery-main img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-main.zoomed img {
            transform: scale(2);
        }
        
        .gallery-badges {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        
        .product-badge {
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .product-badge.featured {
            background: #28a745;
        }
        
        .gallery-nav {
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 6px;
        }
        
        .gallery-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .gallery-dot.active {
            background: white;
            transform: scale(1.2);
        }
        
        .gallery-thumbs {
            display: flex;
            gap: 8px;
            padding: 12px;
            overflow-x: auto;
        }
        
        .gallery-thumb {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .gallery-thumb.active {
            border-color: #0C55AA;
        }
        
        .gallery-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        /* Product Info */
        .product-info {
            background: white;
            padding: 20px;
            margin-bottom: 8px;
        }
        
        .product-brand {
            color: #0C55AA;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .product-title {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .rating-stars {
            display: flex;
            gap: 2px;
        }
        
        .star {
            color: #ffc107;
            font-size: 14px;
        }
        
        .rating-text {
            font-size: 13px;
            color: #666;
        }
        
        .product-sku {
            font-size: 12px;
            color: #999;
            margin-bottom: 16px;
        }
        
        /* Pricing */
        .product-pricing {
            background: white;
            padding: 20px;
            margin-bottom: 8px;
        }
        
        .pricing-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .price-row:last-child {
            border-bottom: none;
        }
        
        .price-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .price-values {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .price-current {
            font-size: 16px;
            font-weight: 700;
            color: #0C55AA;
        }
        
        .price-old {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
        }
        
        .wholesale-note {
            background: #e8f5e8;
            border: 1px solid #28a745;
            color: #2d5a2d;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
            margin-top: 12px;
            text-align: center;
        }

        .admin-note {
            background: #fff3cd;
            border: 1px solid #ffc107;
            color: #856404;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
            margin-top: 12px;
            text-align: center;
        }

        .wholesale-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 8px;
        }

        .admin-badge {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            margin-left: 8px;
        }
        
        /* Stock Status */
        .stock-status {
            background: white;
            padding: 20px;
            margin-bottom: 8px;
        }
        
        .stock-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .stock-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .stock-indicator.in-stock {
            background: #28a745;
        }
        
        .stock-indicator.out-of-stock {
            background: #dc3545;
        }
        
        .stock-text {
            font-size: 14px;
            font-weight: 600;
        }
        
        .stock-text.in-stock {
            color: #28a745;
        }
        
        .stock-text.out-of-stock {
            color: #dc3545;
        }
        
        /* Description */
        .product-description {
            background: white;
            padding: 20px;
            margin-bottom: 8px;
        }
        
        .description-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }
        
        .description-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }
        
        /* Actions */
        .product-actions {
            background: white;
            padding: 20px;
            margin-bottom: 8px;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .quantity-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .quantity-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: #f8f9fa;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quantity-btn:hover {
            background: #e9ecef;
        }
        
        .quantity-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .quantity-input {
            width: 60px;
            height: 40px;
            border: none;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            background: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
        }
        
        .btn-primary {
            flex: 1;
            padding: 14px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(12, 85, 170, 0.4);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-secondary {
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            border-color: #0C55AA;
            color: #0C55AA;
        }
        
        .btn-secondary.active {
            background: #0C55AA;
            border-color: #0C55AA;
            color: white;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .bottom-nav-content {
            max-width: 500px;
            margin: 0 auto;
            display: flex;
            gap: 12px;
        }
        
        .nav-price {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .nav-price-current {
            font-size: 18px;
            font-weight: 700;
            color: #0C55AA;
        }
        
        .nav-price-old {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
        }
        
        @media (max-width: 480px) {
            .main-content {
                margin-top: 65px;
            }
            
            .product-info,
            .product-pricing,
            .stock-status,
            .product-description,
            .product-actions {
                padding: 16px;
            }
            
            .product-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m12 19-7-7 7-7"/>
                    <path d="M19 12H5"/>
                </svg>
            </button>
            <div class="header-title">تفاصيل المنتج</div>
            <div class="header-actions">
                <button class="header-btn" id="share-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                        <polyline points="16,6 12,2 8,6"/>
                        <line x1="12" x2="12" y1="2" y2="15"/>
                    </svg>
                </button>
                <button class="header-btn" id="wishlist-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Product Gallery -->
        <div class="product-gallery">
            <div class="gallery-main" id="gallery-main">
                <img src="/storage/main/general/placeholder.png" alt="Product Image" id="main-image">
                <div class="gallery-badges" id="product-badges">
                    <!-- Badges will be inserted here -->
                </div>
                <div class="gallery-nav" id="gallery-nav" style="display: none;">
                    <!-- Navigation dots will be inserted here -->
                </div>
            </div>
            <div class="gallery-thumbs" id="gallery-thumbs" style="display: none;">
                <!-- Thumbnail images will be inserted here -->
            </div>
        </div>

        <!-- Product Info -->
        <div class="product-info">
            <div class="product-brand" id="product-brand">جاري التحميل...</div>
            <h1 class="product-title" id="product-title">جاري تحميل المنتج...</h1>
            <div class="product-rating" id="product-rating" style="display: none;">
                <div class="rating-stars">
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">☆</span>
                </div>
                <span class="rating-text">(4.2 من 5)</span>
            </div>
            <div class="product-sku" id="product-sku"></div>
        </div>

        <!-- Pricing -->
        <div class="product-pricing">
            <div class="pricing-title">الأسعار</div>
            <div id="pricing-content">
                <!-- Pricing will be inserted here -->
            </div>
        </div>

        <!-- Stock Status -->
        <div class="stock-status">
            <div class="stock-info" id="stock-info">
                <div class="stock-indicator in-stock"></div>
                <div class="stock-text in-stock">متوفر في المخزن</div>
            </div>
        </div>

        <!-- Description -->
        <div class="product-description">
            <div class="description-title">الوصف</div>
            <div class="description-content" id="product-description">
                جاري تحميل الوصف...
            </div>
        </div>

        <!-- Actions -->
        <div class="product-actions">
            <div class="quantity-selector">
                <span class="quantity-label">الكمية:</span>
                <div class="quantity-controls">
                    <button class="quantity-btn" id="decrease-qty">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="5" x2="19" y1="12" y2="12"/>
                        </svg>
                    </button>
                    <input type="number" class="quantity-input" id="quantity" value="1" min="1" max="10">
                    <button class="quantity-btn" id="increase-qty">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="12" x2="12" y1="5" y2="19"/>
                            <line x1="5" x2="19" y1="12" y2="12"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="bottom-nav-content">
            <div class="nav-price">
                <div class="nav-price-current" id="nav-price-current">0 IQD</div>
                <div class="nav-price-old" id="nav-price-old" style="display: none;">0 IQD</div>
            </div>
            <button class="btn-primary" id="add-to-cart-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="8" cy="21" r="1"/>
                    <circle cx="19" cy="21" r="1"/>
                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57L23 6H6"/>
                </svg>
                إضافة للسلة
            </button>
        </div>
    </div>

    <script>
        // App Configuration
        const APP_CONFIG = {
            baseUrl: window.location.origin,
            apiBase: window.location.origin + '/api/v1'
        };

        // Get product ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id') || 1;

        // Product data
        let currentProduct = null;
        let currentUser = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProduct();
            setupEventListeners();
        });

        async function loadProduct() {
            try {
                // Simulate API call - replace with actual API
                const response = await fetch(`${APP_CONFIG.apiBase}/products/${productId}`);
                const data = await response.json();
                
                if (data.error) {
                    showError('المنتج غير موجود');
                    return;
                }
                
                currentProduct = data.data;
                renderProduct(currentProduct);
                
            } catch (error) {
                console.error('Error loading product:', error);
                // Use demo data for now
                loadDemoProduct();
            }
        }

        function loadDemoProduct() {
            currentProduct = {
                id: 1,
                name: 'فلتر هواء أصلي لسيارة تويوتا كامري 2018-2022',
                brand: 'تويوتا الأصلي',
                sku: 'TOY-AF-2018-001',
                price: 45000,
                original_price: 50000,
                wholesale_price: 38000,
                description: 'فلتر هواء أصلي عالي الجودة مصمم خصيصاً لسيارات تويوتا كامري من موديل 2018 إلى 2022. يوفر حماية ممتازة للمحرك ويحسن من أداء السيارة.',
                images: [
                    '/storage/main/general/placeholder.png'
                ],
                in_stock: true,
                quantity: 15,
                rating: 4.2,
                reviews_count: 28
            };
            
            renderProduct(currentProduct);
        }

        function renderProduct(product) {
            // Update header title
            document.querySelector('.header-title').textContent = product.name;
            
            // Update product info
            document.getElementById('product-brand').textContent = product.brand || 'غير محدد';
            document.getElementById('product-title').textContent = product.name;
            document.getElementById('product-sku').textContent = `كود المنتج: ${product.sku || 'غير محدد'}`;
            
            // Update main image
            document.getElementById('main-image').src = product.images?.[0] || '/storage/main/general/placeholder.png';
            
            // Update pricing
            renderPricing(product);
            
            // Update stock status
            renderStockStatus(product);
            
            // Update description
            document.getElementById('product-description').innerHTML = product.description || 'لا يوجد وصف متاح';
            
            // Update bottom nav price
            updateBottomNavPrice(product);
            
            // Update badges
            renderBadges(product);
        }

        function renderPricing(product) {
            const pricingContent = document.getElementById('pricing-content');
            let html = '';

            const isWholesaleCustomer = currentUser && currentUser.is_wholesale;
            const isAdmin = currentUser && currentUser.is_admin;

            // الأدمن يرى جميع الأسعار
            if (isAdmin) {
                // سعر المفرد
                if (product.original_price > product.price) {
                    html += `
                        <div class="price-row">
                            <span class="price-label">سعر المفرد:</span>
                            <div class="price-values">
                                <span class="price-current">${formatPrice(product.price)}</span>
                                <span class="price-old">${formatPrice(product.original_price)}</span>
                            </div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="price-row">
                            <span class="price-label">سعر المفرد:</span>
                            <div class="price-values">
                                <span class="price-current">${formatPrice(product.price)}</span>
                            </div>
                        </div>
                    `;
                }

                // سعر الجملة (للأدمن)
                if (product.wholesale_price && product.wholesale_price > 0) {
                    html += `
                        <div class="price-row">
                            <span class="price-label">سعر الجملة:</span>
                            <div class="price-values">
                                <span class="price-current">${formatPrice(product.wholesale_price)}</span>
                            </div>
                        </div>
                        <div class="admin-note">
                            👑 <strong>عرض الأدمن:</strong> جميع الأسعار مرئية
                        </div>
                    `;
                }
            }
            // عميل الجملة يرى سعر الجملة فقط
            else if (isWholesaleCustomer && product.wholesale_price && product.wholesale_price > 0) {
                html += `
                    <div class="price-row">
                        <span class="price-label">سعر الجملة:</span>
                        <div class="price-values">
                            <span class="price-current">${formatPrice(product.wholesale_price)}</span>
                        </div>
                    </div>
                    <div class="wholesale-note">
                        ✓ <strong>عميل جملة معتمد:</strong> تتمتع بأسعار خاصة
                    </div>
                `;
            }
            // عميل المفرد أو غير مسجل يرى سعر المفرد فقط
            else {
                if (product.original_price > product.price) {
                    html += `
                        <div class="price-row">
                            <span class="price-label">السعر:</span>
                            <div class="price-values">
                                <span class="price-current">${formatPrice(product.price)}</span>
                                <span class="price-old">${formatPrice(product.original_price)}</span>
                            </div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="price-row">
                            <span class="price-label">السعر:</span>
                            <div class="price-values">
                                <span class="price-current">${formatPrice(product.price)}</span>
                            </div>
                        </div>
                    `;
                }

                // إظهار رسالة للتسجيل كعميل جملة (فقط للغير مسجلين)
                if (!currentUser && product.wholesale_price && product.wholesale_price > 0) {
                    html += `
                        <div class="wholesale-note">
                            💼 <strong>للتجار:</strong> <a href="/mobile-register.html" style="color: #0C55AA;">سجل كعميل جملة</a> للحصول على أسعار خاصة
                        </div>
                    `;
                }
            }

            pricingContent.innerHTML = html;
        }

        function renderStockStatus(product) {
            const stockInfo = document.getElementById('stock-info');
            const indicator = stockInfo.querySelector('.stock-indicator');
            const text = stockInfo.querySelector('.stock-text');
            
            if (product.in_stock && product.quantity > 0) {
                indicator.className = 'stock-indicator in-stock';
                text.className = 'stock-text in-stock';
                text.textContent = `متوفر (${product.quantity} قطعة)`;
            } else {
                indicator.className = 'stock-indicator out-of-stock';
                text.className = 'stock-text out-of-stock';
                text.textContent = 'غير متوفر';
            }
        }

        function renderBadges(product) {
            const badgesContainer = document.getElementById('product-badges');
            let html = '';
            
            if (product.original_price > product.price) {
                const discount = Math.round((1 - product.price / product.original_price) * 100);
                html += `<div class="product-badge">خصم ${discount}%</div>`;
            }
            
            if (product.is_featured) {
                html += `<div class="product-badge featured">مميز</div>`;
            }
            
            badgesContainer.innerHTML = html;
        }

        function updateBottomNavPrice(product) {
            const currentPriceEl = document.getElementById('nav-price-current');
            const oldPriceEl = document.getElementById('nav-price-old');

            const isWholesaleCustomer = currentUser && currentUser.is_wholesale;
            const isAdmin = currentUser && currentUser.is_admin;

            let displayPrice = product.price;
            let showOldPrice = false;

            // تحديد السعر المناسب حسب نوع المستخدم
            if (isAdmin) {
                // الأدمن يرى سعر المفرد في الشريط السفلي (يمكن تغييره حسب الحاجة)
                displayPrice = product.price;
                showOldPrice = product.original_price > product.price;
            } else if (isWholesaleCustomer && product.wholesale_price && product.wholesale_price > 0) {
                // عميل الجملة يرى سعر الجملة
                displayPrice = product.wholesale_price;
                showOldPrice = false; // سعر الجملة لا يحتاج سعر قديم
            } else {
                // عميل المفرد أو غير مسجل يرى سعر المفرد
                displayPrice = product.price;
                showOldPrice = product.original_price > product.price;
            }

            currentPriceEl.textContent = formatPrice(displayPrice);

            if (showOldPrice) {
                oldPriceEl.textContent = formatPrice(product.original_price);
                oldPriceEl.style.display = 'block';
            } else {
                oldPriceEl.style.display = 'none';
            }
        }

        function setupEventListeners() {
            // Quantity controls
            document.getElementById('decrease-qty').addEventListener('click', () => {
                const input = document.getElementById('quantity');
                const value = parseInt(input.value);
                if (value > 1) {
                    input.value = value - 1;
                }
            });
            
            document.getElementById('increase-qty').addEventListener('click', () => {
                const input = document.getElementById('quantity');
                const value = parseInt(input.value);
                const max = currentProduct?.quantity || 10;
                if (value < max) {
                    input.value = value + 1;
                }
            });
            
            // Add to cart
            document.getElementById('add-to-cart-btn').addEventListener('click', addToCart);
            
            // Wishlist
            document.getElementById('wishlist-btn').addEventListener('click', toggleWishlist);
            
            // Share
            document.getElementById('share-btn').addEventListener('click', shareProduct);
            
            // Image zoom
            document.getElementById('gallery-main').addEventListener('click', toggleImageZoom);
        }

        async function addToCart() {
            if (!currentProduct) return;
            
            const quantity = parseInt(document.getElementById('quantity').value);
            const btn = document.getElementById('add-to-cart-btn');
            
            // Show loading
            btn.disabled = true;
            btn.innerHTML = `
                <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                جاري الإضافة...
            `;
            
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Success
                btn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                    تمت الإضافة
                `;
                
                // Reset after 2 seconds
                setTimeout(() => {
                    btn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="8" cy="21" r="1"/>
                            <circle cx="19" cy="21" r="1"/>
                            <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57L23 6H6"/>
                        </svg>
                        إضافة للسلة
                    `;
                    btn.disabled = false;
                }, 2000);
                
            } catch (error) {
                console.error('Error adding to cart:', error);
                btn.innerHTML = 'حدث خطأ';
                setTimeout(() => {
                    btn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="8" cy="21" r="1"/>
                            <circle cx="19" cy="21" r="1"/>
                            <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57L23 6H6"/>
                        </svg>
                        إضافة للسلة
                    `;
                    btn.disabled = false;
                }, 2000);
            }
        }

        function toggleWishlist() {
            const btn = document.getElementById('wishlist-btn');
            btn.classList.toggle('active');
            
            if (btn.classList.contains('active')) {
                btn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"/>
                    </svg>
                `;
            } else {
                btn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"/>
                    </svg>
                `;
            }
        }

        function shareProduct() {
            if (navigator.share && currentProduct) {
                navigator.share({
                    title: currentProduct.name,
                    text: currentProduct.description,
                    url: window.location.href
                });
            } else {
                // Fallback - copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('تم نسخ رابط المنتج');
                });
            }
        }

        function toggleImageZoom() {
            const galleryMain = document.getElementById('gallery-main');
            galleryMain.classList.toggle('zoomed');
        }

        function formatPrice(price) {
            if (!price) return '0 IQD';
            const numPrice = parseFloat(price);
            return numPrice.toLocaleString('ar-IQ') + ' IQD';
        }

        function showError(message) {
            alert(message);
        }

        function goBack() {
            if (document.referrer) {
                window.history.back();
            } else {
                window.location.href = 'mobile-app.html';
            }
        }

        // Add spin animation for loading
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
