<?php
// Simple endpoint to return Ecommerce Categories shortcode for mobile
header('Content-Type: text/html; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, X-CSRF-TOKEN');

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);
$shortcode = $input['shortcode'] ?? '[ecommerce-categories style="slider"][/ecommerce-categories]';

// Check if we can access Laravel
$laravelPath = dirname(dirname(__FILE__)) . '/index.php';

if (file_exists($laravelPath)) {
    try {
        // Try to bootstrap Laravel
        require_once $laravelPath;
        
        // Check if ecommerce plugin is active
        if (function_exists('is_plugin_active') && is_plugin_active('ecommerce')) {
            // Try to render the shortcode
            if (function_exists('do_shortcode')) {
                $html = do_shortcode($shortcode);
                
                // Add mobile-specific styling
                $mobileStyles = '
                <style>
                .tp-category-area {
                    background: #F3F5F7 !important;
                    border-radius: 15px !important;
                    padding: 20px !important;
                    margin: 0 !important;
                }
                .tp-category-slider {
                    overflow-x: auto !important;
                    scrollbar-width: none !important;
                    -ms-overflow-style: none !important;
                }
                .tp-category-slider::-webkit-scrollbar {
                    display: none !important;
                }
                .tp-category-item {
                    min-width: 80px !important;
                    background: white !important;
                    border-radius: 12px !important;
                    padding: 16px 12px !important;
                    text-align: center !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
                    margin: 0 6px !important;
                    transition: all 0.3s ease !important;
                }
                .tp-category-item:hover {
                    transform: translateY(-2px) !important;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
                }
                .tp-category-icon {
                    width: 40px !important;
                    height: 40px !important;
                    margin: 0 auto 8px !important;
                    border-radius: 8px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-size: 20px !important;
                }
                .tp-category-title {
                    font-size: 12px !important;
                    font-weight: 600 !important;
                    line-height: 1.3 !important;
                    color: #333 !important;
                }
                .tp-section-title {
                    font-size: 18px !important;
                    font-weight: 700 !important;
                    color: #333 !important;
                    margin-bottom: 16px !important;
                    text-align: center !important;
                }
                </style>';
                
                echo $mobileStyles . $html;
                exit;
            }
        }
    } catch (Exception $e) {
        // Laravel bootstrap failed, continue to fallback
        error_log('Laravel bootstrap failed: ' . $e->getMessage());
    }
}

// Fallback HTML if Laravel is not available
?>
<div class="main-categories-fallback" style="background: #F3F5F7; border-radius: 15px; padding: 20px;">
    <h3 style="
        font-size: 18px;
        font-weight: 700;
        color: #333;
        margin-bottom: 16px;
        text-align: center;
        font-family: 'Cairo', sans-serif;
    ">📂 الفئات الرئيسية</h3>
    
    <div style="display: flex; gap: 12px; overflow-x: auto; padding: 4px 0; scrollbar-width: none; -ms-overflow-style: none;">
        <div class="main-category-item" style="
            min-width: 80px;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        " onclick="window.location.href='/products?category=engine'">
            <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
                color: white;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 8px;
                font-size: 20px;
            ">🔧</div>
            <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif;">قطع المحرك</div>
        </div>
        
        <div class="main-category-item" style="
            min-width: 80px;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        " onclick="window.location.href='/products?category=tires'">
            <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
                color: white;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 8px;
                font-size: 20px;
            ">🛞</div>
            <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif;">الإطارات</div>
        </div>
        
        <div class="main-category-item" style="
            min-width: 80px;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        " onclick="window.location.href='/products?category=brakes'">
            <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, #dc3545 0%, #e55a5a 100%);
                color: white;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 8px;
                font-size: 20px;
            ">🛑</div>
            <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif;">الفرامل</div>
        </div>
        
        <div class="main-category-item" style="
            min-width: 80px;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        " onclick="window.location.href='/products?category=lights'">
            <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, #ffc107 0%, #ffcd39 100%);
                color: white;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 8px;
                font-size: 20px;
            ">💡</div>
            <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif;">الإضاءة</div>
        </div>
        
        <div class="main-category-item" style="
            min-width: 80px;
            background: white;
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        " onclick="window.location.href='/products?category=electrical'">
            <div style="
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, #6f42c1 0%, #8a63d2 100%);
                color: white;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 8px;
                font-size: 20px;
            ">⚡</div>
            <div style="font-size: 12px; font-weight: 600; font-family: 'Cairo', sans-serif;">القطع الكهربائية</div>
        </div>
    </div>
</div>

<style>
.main-category-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('📂 Main categories fallback loaded');
});
</script>
