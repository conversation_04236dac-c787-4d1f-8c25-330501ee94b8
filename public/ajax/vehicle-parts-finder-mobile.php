<?php
// Simple endpoint to return Vehicle Parts Finder shortcode for mobile
header('Content-Type: text/html; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, X-CSRF-TOKEN');

// Get POST data if available
$input = json_decode(file_get_contents('php://input'), true);
$shortcode = $input['shortcode'] ?? '[vehicle_parts_finder][/vehicle_parts_finder]';

// Check if we can access Laravel
$laravelPath = dirname(dirname(__FILE__)) . '/index.php';

if (file_exists($laravelPath)) {
    try {
        // Try to bootstrap Laravel
        require_once $laravelPath;

        // Check if vehicle parts finder plugin is active
        if (function_exists('is_plugin_active') && is_plugin_active('vehicle-parts-finder')) {
            // Try to render the shortcode
            if (function_exists('render_vehicle_parts_finder_mobile_shortcode')) {
                $html = render_vehicle_parts_finder_mobile_shortcode();

                // Add mobile-specific styling
                $mobileStyles = '
                <style>
                .vpf-search-section {
                    background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%) !important;
                    border-radius: 20px !important;
                    padding: 24px !important;
                    color: white !important;
                    box-shadow: 0 8px 25px rgba(12, 85, 170, 0.3) !important;
                }
                .vpf-container {
                    background: transparent !important;
                }
                .vpf-search-title {
                    color: white !important;
                    text-align: center !important;
                    font-size: 20px !important;
                    font-weight: 700 !important;
                    margin-bottom: 8px !important;
                }
                .vpf-filter-group select {
                    background: rgba(255, 255, 255, 0.1) !important;
                    border: 1px solid rgba(255, 255, 255, 0.2) !important;
                    border-radius: 12px !important;
                    padding: 14px 16px !important;
                    color: white !important;
                    font-size: 16px !important;
                    backdrop-filter: blur(10px) !important;
                }
                .vpf-search-button {
                    background: white !important;
                    color: #0C55AA !important;
                    border: none !important;
                    padding: 16px !important;
                    border-radius: 12px !important;
                    font-size: 16px !important;
                    font-weight: 700 !important;
                    width: 100% !important;
                    margin-top: 8px !important;
                }
                </style>';

                echo $mobileStyles . $html;
                exit;
            } elseif (function_exists('do_shortcode')) {
                $html = do_shortcode($shortcode);
                echo $html;
                exit;
            }
        }
    } catch (Exception $e) {
        // Laravel bootstrap failed, continue to fallback
        error_log('Laravel bootstrap failed: ' . $e->getMessage());
    }
}

// Enhanced Fallback HTML if Laravel is not available
?>
<div class="vpf-header" style="text-align: center; margin-bottom: 20px;">
    <div class="vpf-icon" style="
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        font-size: 28px;
    ">🚗</div>
    <h2 class="vpf-title" style="
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 8px;
        color: white;
        font-family: 'Cairo', sans-serif;
    ">البحث عن قطع الغيار</h2>
    <p class="vpf-subtitle" style="
        font-size: 14px;
        opacity: 0.9;
        color: white;
        font-family: 'Cairo', sans-serif;
    ">اختر سيارتك للعثور على القطع المناسبة</p>
</div>
<form class="vpf-form" id="vpf-form" style="display: grid; gap: 12px;">
    <select class="vpf-select" id="vehicle-type" data-level="1" required style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 14px 16px;
        color: white;
        font-size: 16px;
        font-family: 'Cairo', sans-serif;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    ">
        <option value="" style="background: #0C55AA; color: white;">النوع</option>
        <option value="1" style="background: #0C55AA; color: white;">سيارات</option>
        <option value="2" style="background: #0C55AA; color: white;">شاحنات</option>
        <option value="3" style="background: #0C55AA; color: white;">دراجات نارية</option>
    </select>
    <select class="vpf-select" id="vehicle-brand" data-level="2" disabled style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 14px 16px;
        color: white;
        font-size: 16px;
        font-family: 'Cairo', sans-serif;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    ">
        <option value="" style="background: #0C55AA; color: white;">الماركة</option>
    </select>
    <select class="vpf-select" id="vehicle-model" data-level="3" disabled style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 14px 16px;
        color: white;
        font-size: 16px;
        font-family: 'Cairo', sans-serif;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    ">
        <option value="" style="background: #0C55AA; color: white;">الموديل</option>
    </select>
    <select class="vpf-select" id="vehicle-year" data-level="4" disabled style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 14px 16px;
        color: white;
        font-size: 16px;
        font-family: 'Cairo', sans-serif;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    ">
        <option value="" style="background: #0C55AA; color: white;">السنة</option>
    </select>
    <button type="submit" class="vpf-btn" style="
        background: white;
        color: #0C55AA;
        border: none;
        padding: 16px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 700;
        font-family: 'Cairo', sans-serif;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    ">
        🔍 عرض القطع المتاحة
    </button>
</form>

<style>
.vpf-header {
    text-align: center;
    margin-bottom: 20px;
}

.vpf-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 28px;
}

.vpf-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 8px;
}

.vpf-subtitle {
    font-size: 14px;
    opacity: 0.9;
}

.vpf-form {
    display: grid;
    gap: 12px;
}

.vpf-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 14px 16px;
    color: white;
    font-size: 16px;
    font-family: 'Cairo', sans-serif;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.vpf-select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
}

.vpf-select option {
    background: #0C55AA;
    color: white;
}

.vpf-btn {
    background: white;
    color: #0C55AA;
    border: none;
    padding: 16px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.vpf-btn:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
}
</style>

<script>
// Initialize VPF functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚗 Vehicle Parts Finder loaded via AJAX');
    
    // This will be handled by the main app
    if (window.app && typeof window.app.setupCustomVPFEvents === 'function') {
        window.app.setupCustomVPFEvents();
    }
});
</script>
