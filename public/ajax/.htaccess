RewriteEngine On

# Handle ecommerce-categories-mobile requests
RewriteRule ^ecommerce-categories-mobile$ ecommerce-categories-mobile.php [L]

# Handle simple-slider-mobile requests  
RewriteRule ^simple-slider-mobile$ simple-slider-mobile.php [L]

# Handle vehicle-parts-finder-mobile requests
RewriteRule ^vehicle-parts-finder-mobile$ vehicle-parts-finder-mobile.php [L]

# Set proper headers for AJAX requests
<FilesMatch "\.(php)$">
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, X-Requested-With, X-CSRF-TOKEN"
</FilesMatch>
