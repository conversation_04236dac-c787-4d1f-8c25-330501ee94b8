<?php
// Simple endpoint to return Simple Slider shortcode for mobile
header('Content-Type: text/html; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With, X-CSRF-TOKEN');

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);
$shortcode = $input['shortcode'] ?? '[simple-slider key="home-slider"][/simple-slider]';

// Check if we can access Laravel
$laravelPath = dirname(dirname(__FILE__)) . '/index.php';

if (file_exists($laravelPath)) {
    try {
        // Try to bootstrap Laravel
        require_once $laravelPath;
        
        // Check if simple slider plugin is active
        if (function_exists('is_plugin_active') && is_plugin_active('simple-slider')) {
            // Try to render the shortcode
            if (function_exists('do_shortcode')) {
                $html = do_shortcode($shortcode);
                
                // Add mobile-specific styling
                $mobileStyles = '
                <style>
                .tp-slider-area {
                    border-radius: 20px !important;
                    overflow: hidden !important;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
                    margin: 0 !important;
                }
                .tp-slider-item {
                    height: 200px !important;
                    min-height: 200px !important;
                }
                .tp-slider-content {
                    padding: 20px !important;
                }
                .tp-slider-title {
                    font-size: 18px !important;
                    line-height: 1.2 !important;
                    margin-bottom: 8px !important;
                }
                .tp-slider-text {
                    font-size: 14px !important;
                    margin-bottom: 12px !important;
                }
                .tp-slider-btn {
                    padding: 8px 16px !important;
                    font-size: 12px !important;
                    border-radius: 6px !important;
                }
                .swiper-pagination-bullet {
                    background: rgba(255, 255, 255, 0.5) !important;
                }
                .swiper-pagination-bullet-active {
                    background: white !important;
                }
                .swiper-button-next,
                .swiper-button-prev {
                    width: 40px !important;
                    height: 40px !important;
                    background: rgba(255, 255, 255, 0.9) !important;
                    border-radius: 50% !important;
                    color: #333 !important;
                }
                @media (max-width: 768px) {
                    .tp-slider-item {
                        height: 180px !important;
                    }
                    .tp-slider-title {
                        font-size: 16px !important;
                    }
                    .tp-slider-text {
                        font-size: 12px !important;
                    }
                }
                </style>';
                
                echo $mobileStyles . $html;
                exit;
            }
        }
    } catch (Exception $e) {
        // Laravel bootstrap failed, continue to fallback
        error_log('Laravel bootstrap failed: ' . $e->getMessage());
    }
}

// Fallback HTML if Laravel is not available
?>
<div class="simple-slider-fallback" style="
    height: 200px;
    background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
">
    <!-- Background shapes -->
    <div style="
        position: absolute;
        top: -50px;
        right: -50px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
    "></div>
    <div style="
        position: absolute;
        bottom: -30px;
        left: -30px;
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
    "></div>
    
    <div style="z-index: 2; position: relative;">
        <div style="font-size: 48px; margin-bottom: 16px;">🚗</div>
        <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 8px; font-family: 'Cairo', sans-serif;">دليل قطع الغيار</h2>
        <p style="font-size: 16px; opacity: 0.9; font-family: 'Cairo', sans-serif;">أفضل قطع الغيار بأسعار منافسة</p>
        <div style="
            margin-top: 16px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: inline-block;
            font-size: 12px;
            font-weight: 600;
        ">جودة عالية • أسعار منافسة • توصيل سريع</div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎠 Simple slider fallback loaded');
});
</script>
