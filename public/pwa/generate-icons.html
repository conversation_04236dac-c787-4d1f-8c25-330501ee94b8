<!DOCTYPE html>
<html>
<head>
    <title>Generate PWA Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon { margin: 10px; display: inline-block; }
        canvas { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>PWA Icon Generator</h1>
    <p>This page generates the required PWA icons.</p>
    
    <div class="icon">
        <h3>32x32</h3>
        <canvas id="icon32" width="32" height="32"></canvas>
    </div>
    
    <div class="icon">
        <h3>192x192</h3>
        <canvas id="icon192" width="192" height="192"></canvas>
    </div>
    
    <div class="icon">
        <h3>512x512</h3>
        <canvas id="icon512" width="512" height="512"></canvas>
    </div>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#0C55AA');
            gradient.addColorStop(1, '#3D73C4');
            
            // Draw background with rounded corners
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            
            // Draw car icon
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🚗', size / 2, size / 2);
            
            // Add text for larger icons
            if (size >= 192) {
                ctx.font = `${size * 0.08}px Arial`;
                ctx.fillText('دليل قطع الغيار', size / 2, size * 0.8);
            }
        }
        
        // Generate icons
        drawIcon(document.getElementById('icon32'), 32);
        drawIcon(document.getElementById('icon192'), 192);
        drawIcon(document.getElementById('icon512'), 512);
        
        // Download function
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Add download buttons
        document.body.innerHTML += `
            <div style="margin-top: 20px;">
                <button onclick="downloadIcon('icon32', 'icon-32x32.png')">Download 32x32</button>
                <button onclick="downloadIcon('icon192', 'icon-192x192.png')">Download 192x192</button>
                <button onclick="downloadIcon('icon512', 'icon-512x512.png')">Download 512x512</button>
            </div>
        `;
    </script>
</body>
</html>
