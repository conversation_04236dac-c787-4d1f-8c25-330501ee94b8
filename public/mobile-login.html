<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - دليل قطع الغيار</title>
    <meta name="description" content="تسجيل الدخول إلى حسابك في دليل قطع الغيار للسيارات">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="دليل قطع الغيار">
    
    <!-- Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/pwa/icon-192x192.png">
    <link rel="manifest" href="/pwa/manifest.json">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .login-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .login-form {
            padding: 30px 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #0C55AA;
            background: white;
            box-shadow: 0 0 0 3px rgba(12, 85, 170, 0.1);
        }

        .form-input::placeholder {
            color: #999;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: #666;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #0C55AA;
        }

        .remember-me label {
            font-size: 14px;
            color: #666;
            cursor: pointer;
        }

        .forgot-password {
            color: #0C55AA;
            text-decoration: none;
            font-size: 14px;
            font-weight: 600;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-button {
            width: 100%;
            background: linear-gradient(135deg, #0C55AA 0%, #1e3c72 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(12, 85, 170, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }

        .register-link {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .register-link p {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .register-button {
            color: #0C55AA;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
        }

        .register-button:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #fcc;
        }

        .success-message {
            background: #efe;
            color: #363;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #cfc;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0C55AA;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .login-header {
                padding: 25px 15px;
            }
            
            .login-form {
                padding: 25px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <button class="back-button" onclick="goBack()">←</button>
            <h1>تسجيل الدخول</h1>
            <p>مرحباً بك في دليل قطع الغيار</p>
        </div>

        <div class="login-form">
            <div id="message-container"></div>
            
            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <p>جاري تسجيل الدخول...</p>
            </div>

            <form id="loginForm" style="display: block;">
                <div class="form-group">
                    <label for="username" class="form-label">البريد الإلكتروني أو رقم الهاتف</label>
                    <input type="text" id="username" name="username" class="form-input" 
                           placeholder="أدخل بريدك الإلكتروني أو رقم هاتفك" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="password-container">
                        <input type="password" id="password" name="password" class="form-input" 
                               placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">👁️</button>
                    </div>
                </div>

                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">تذكرني</label>
                    </div>
                    <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="login-button" id="loginButton">
                    تسجيل الدخول
                </button>
            </form>
        </div>

        <div class="register-link">
            <p>ليس لديك حساب؟</p>
            <a href="/mobile-register.html" class="register-button">إنشاء حساب جديد</a>
        </div>
    </div>

    <script>
        // App Configuration
        const APP_CONFIG = {
            apiBase: '/api/v1',
            baseUrl: window.location.origin
        };

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginForm();
            checkAuthStatus();
        });

        function initializeLoginForm() {
            const form = document.getElementById('loginForm');
            form.addEventListener('submit', handleLogin);
        }

        function checkAuthStatus() {
            // Check if user is already logged in
            const token = localStorage.getItem('auth_token');
            if (token) {
                // Redirect to main app
                window.location.href = '/mobile-app.html';
            }
        }

        async function handleLogin(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const username = formData.get('username');
            const password = formData.get('password');
            const remember = formData.get('remember');

            if (!username || !password) {
                showMessage('يرجى إدخال جميع البيانات المطلوبة', 'error');
                return;
            }

            showLoading(true);

            try {
                const response = await fetch('/customer/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        remember: remember ? 1 : 0
                    })
                });

                const data = await response.json();

                if (response.ok && data.success !== false) {
                    // Login successful
                    if (data.token) {
                        localStorage.setItem('auth_token', data.token);
                    }
                    
                    showMessage('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    setTimeout(() => {
                        const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/mobile-app.html';
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    // Login failed
                    const errorMessage = data.message || 'فشل في تسجيل الدخول. يرجى التحقق من البيانات المدخلة.';
                    showMessage(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                showMessage('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                showLoading(false);
            }
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }

        function showForgotPassword() {
            // Implement forgot password functionality
            alert('سيتم إضافة وظيفة استعادة كلمة المرور قريباً');
        }

        function showMessage(message, type) {
            const container = document.getElementById('message-container');
            const className = type === 'error' ? 'error-message' : 'success-message';
            
            container.innerHTML = `<div class="${className}">${message}</div>`;
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const form = document.getElementById('loginForm');
            const button = document.getElementById('loginButton');
            
            if (show) {
                loading.style.display = 'block';
                form.style.display = 'none';
                button.disabled = true;
            } else {
                loading.style.display = 'none';
                form.style.display = 'block';
                button.disabled = false;
            }
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/mobile-app.html';
            }
        }
    </script>
</body>
</html>
