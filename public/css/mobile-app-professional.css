/* ===================================
   Professional Mobile App Styles
   Inspired by Amazon, eBay, and other major apps
   =================================== */

:root {
    --primary-color: #0C55AA;
    --primary-light: #3D73C4;
    --primary-dark: #094488;
    --secondary-color: #6c7a91;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 4px 15px rgba(0, 0, 0, 0.08);
    --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.15);
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===================================
   Enhanced Animations
   =================================== */

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* ===================================
   Professional Loading States
   =================================== */

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-title {
    height: 20px;
    border-radius: 4px;
    margin-bottom: 12px;
    width: 70%;
}

.skeleton-image {
    height: 140px;
    border-radius: var(--radius-medium);
    margin-bottom: 12px;
}

.skeleton-button {
    height: 40px;
    border-radius: var(--radius-medium);
    width: 100%;
}

/* ===================================
   Enhanced Product Cards
   =================================== */

.product-card-enhanced {
    background: white;
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    position: relative;
    transform: translateY(0);
}

.product-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.product-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.product-card-enhanced:hover::before {
    transform: scaleX(1);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    background: var(--light-color);
}

.product-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card-enhanced:hover .product-image-container img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.product-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    color: white;
    backdrop-filter: blur(10px);
}

.badge-sale {
    background: rgba(220, 53, 69, 0.9);
}

.badge-new {
    background: rgba(40, 167, 69, 0.9);
}

.badge-featured {
    background: rgba(255, 193, 7, 0.9);
    color: #333;
}

.product-quick-actions {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
}

.product-card-enhanced:hover .product-quick-actions {
    opacity: 1;
    transform: translateX(0);
}

.quick-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.quick-action-btn.active {
    background: var(--danger-color);
    color: white;
}

/* ===================================
   Enhanced Search Components
   =================================== */

.search-container-enhanced {
    position: relative;
    background: white;
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    transition: var(--transition);
}

.search-container-enhanced:focus-within {
    box-shadow: 0 0 0 3px rgba(12, 85, 170, 0.1);
    transform: translateY(-2px);
}

.search-input-enhanced {
    width: 100%;
    padding: 16px 60px 16px 20px;
    border: none;
    font-size: 16px;
    font-family: 'Cairo', sans-serif;
    outline: none;
    background: transparent;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 0 0 var(--radius-large) var(--radius-large);
    box-shadow: var(--shadow-heavy);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestions.show {
    display: block;
    animation: slideInDown 0.3s ease;
}

.suggestion-item {
    padding: 12px 20px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.suggestion-item:hover {
    background: var(--light-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    width: 32px;
    height: 32px;
    background: var(--light-color);
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.suggestion-text {
    flex: 1;
}

.suggestion-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.suggestion-category {
    font-size: 12px;
    color: var(--secondary-color);
}

/* ===================================
   Enhanced Category Cards
   =================================== */

.category-card-enhanced {
    background: white;
    border-radius: var(--radius-large);
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: #333;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.category-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.category-card-enhanced:hover::before {
    left: 100%;
}

.category-card-enhanced:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

.category-icon-enhanced {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 28px;
    transition: var(--transition);
}

.category-card-enhanced:hover .category-icon-enhanced {
    transform: scale(1.1) rotate(5deg);
}

/* ===================================
   Enhanced Navigation
   =================================== */

.bottom-nav-enhanced {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid var(--border-color);
    padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
}

.nav-item-enhanced {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    text-decoration: none;
    color: var(--secondary-color);
    transition: var(--transition);
    border-radius: var(--radius-medium);
    min-width: 60px;
    position: relative;
}

.nav-item-enhanced:hover,
.nav-item-enhanced.active {
    color: var(--primary-color);
    background: rgba(12, 85, 170, 0.1);
    transform: translateY(-2px);
}

.nav-item-enhanced.active::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
}

.nav-icon-enhanced {
    font-size: 22px;
    margin-bottom: 4px;
    transition: var(--transition);
}

.nav-item-enhanced:hover .nav-icon-enhanced {
    transform: scale(1.1);
}

.nav-text-enhanced {
    font-size: 11px;
    font-weight: 600;
}

/* ===================================
   Enhanced Buttons
   =================================== */

.btn-enhanced {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn-enhanced:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(12, 85, 170, 0.3);
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(12, 85, 170, 0.4);
}

.btn-secondary-enhanced {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary-enhanced:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-success-enhanced {
    background: linear-gradient(135deg, var(--success-color) 0%, #34ce57 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-danger-enhanced {
    background: linear-gradient(135deg, var(--danger-color) 0%, #e55a5a 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* ===================================
   Enhanced Forms
   =================================== */

.form-group-enhanced {
    margin-bottom: 20px;
    position: relative;
}

.form-label-enhanced {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 14px;
}

.form-control-enhanced {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-family: 'Cairo', sans-serif;
    transition: var(--transition);
    background: white;
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(12, 85, 170, 0.1);
    transform: translateY(-1px);
}

.form-control-enhanced.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-error-message {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* ===================================
   Enhanced Modals and Overlays
   =================================== */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    padding: 24px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-heavy);
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--dark-color);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

/* ===================================
   Enhanced Toast Notifications
   =================================== */

.toast-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.toast-enhanced {
    background: white;
    border-radius: var(--radius-medium);
    padding: 16px 20px;
    box-shadow: var(--shadow-heavy);
    transform: translateX(400px);
    transition: var(--transition);
    max-width: 300px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 4px solid var(--primary-color);
}

.toast-enhanced.show {
    transform: translateX(0);
}

.toast-enhanced.success {
    border-left-color: var(--success-color);
}

.toast-enhanced.error {
    border-left-color: var(--danger-color);
}

.toast-enhanced.warning {
    border-left-color: var(--warning-color);
}

.toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.toast-icon.success {
    background: var(--success-color);
}

.toast-icon.error {
    background: var(--danger-color);
}

.toast-icon.warning {
    background: var(--warning-color);
}

.toast-icon.info {
    background: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.toast-message {
    font-size: 13px;
    color: var(--secondary-color);
    line-height: 1.4;
}

/* ===================================
   Responsive Design
   =================================== */

@media (max-width: 480px) {
    .btn-enhanced {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .form-control-enhanced {
        padding: 12px 14px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .modal-content {
        margin: 20px;
        max-width: calc(100% - 40px);
    }
    
    .toast-container {
        right: 16px;
        left: 16px;
    }
    
    .toast-enhanced {
        max-width: none;
    }
}

/* ===================================
   Dark Mode Support (Future)
   =================================== */

@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #2d3748;
        --dark-color: #f7fafc;
        --border-color: #4a5568;
    }
    
    body {
        background: #1a202c;
        color: #f7fafc;
    }
    
    .product-card-enhanced,
    .category-card-enhanced,
    .modal-content,
    .toast-enhanced,
    .search-container-enhanced {
        background: #2d3748;
        color: #f7fafc;
    }
    
    .bottom-nav-enhanced {
        background: #2d3748;
        border-top-color: #4a5568;
    }
}
