/* ===================================
   PWA Mobile Enhancements for Dalil Auto Parts
   =================================== */

:root {
    --dalil-primary: #0C55AA;
    --dalil-primary-light: #3D73C4;
    --dalil-primary-dark: #094488;
    --dalil-secondary: #6c7a91;
    --dalil-success: #28a745;
    --dalil-warning: #ffc107;
    --dalil-danger: #dc3545;
    --dalil-white: #ffffff;
    --dalil-light: #f8f9fa;
    --dalil-dark: #343a40;
    --dalil-border: #eaebed;
    --dalil-shadow: 0 4px 12px rgba(12, 85, 170, 0.15);
    --dalil-radius: 12px;
    --dalil-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===================================
   Touch-Friendly Improvements
   =================================== */

/* Minimum touch target size */
.btn, button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
}

/* Enhanced button styles */
.btn-mobile-enhanced {
    padding: 16px 24px;
    border-radius: var(--dalil-radius);
    font-weight: 600;
    font-size: 16px;
    transition: var(--dalil-transition);
    box-shadow: var(--dalil-shadow);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-mobile-enhanced:hover,
.btn-mobile-enhanced:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(12, 85, 170, 0.25);
}

.btn-mobile-enhanced:active {
    transform: translateY(0);
}

/* ===================================
   Mobile Navigation Enhancements
   =================================== */

.mobile-nav-enhanced {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--dalil-white);
    border-top: 1px solid var(--dalil-border);
    padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-nav-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 500px;
    margin: 0 auto;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    text-decoration: none;
    color: var(--dalil-secondary);
    transition: var(--dalil-transition);
    border-radius: 8px;
    min-width: 60px;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
    color: var(--dalil-primary);
    background: rgba(12, 85, 170, 0.1);
}

.mobile-nav-icon {
    font-size: 24px;
    margin-bottom: 4px;
}

.mobile-nav-text {
    font-size: 12px;
    font-weight: 500;
}

.mobile-nav-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--dalil-danger);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* ===================================
   Enhanced Search Bar
   =================================== */

.search-bar-mobile {
    position: relative;
    margin: 16px;
    background: var(--dalil-white);
    border-radius: var(--dalil-radius);
    box-shadow: var(--dalil-shadow);
    overflow: hidden;
}

.search-input-mobile {
    width: 100%;
    padding: 16px 50px 16px 20px;
    border: none;
    font-size: 16px;
    background: transparent;
    outline: none;
}

.search-input-mobile::placeholder {
    color: var(--dalil-secondary);
}

.search-btn-mobile {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--dalil-primary);
    color: white;
    border: none;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--dalil-transition);
}

.search-btn-mobile:hover {
    background: var(--dalil-primary-dark);
}

/* ===================================
   Product Cards Mobile
   =================================== */

.product-card-mobile {
    background: var(--dalil-white);
    border-radius: var(--dalil-radius);
    box-shadow: var(--dalil-shadow);
    overflow: hidden;
    margin-bottom: 16px;
    transition: var(--dalil-transition);
}

.product-card-mobile:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(12, 85, 170, 0.2);
}

.product-image-mobile {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.product-image-mobile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--dalil-transition);
}

.product-card-mobile:hover .product-image-mobile img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--dalil-danger);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.product-info-mobile {
    padding: 16px;
}

.product-title-mobile {
    font-size: 16px;
    font-weight: 600;
    color: var(--dalil-dark);
    margin-bottom: 8px;
    line-height: 1.4;
}

.product-price-mobile {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.price-current {
    font-size: 18px;
    font-weight: 700;
    color: var(--dalil-primary);
}

.price-old {
    font-size: 14px;
    color: var(--dalil-secondary);
    text-decoration: line-through;
}

.product-actions-mobile {
    display: flex;
    gap: 8px;
}

.btn-add-cart-mobile {
    flex: 1;
    background: var(--dalil-primary);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--dalil-transition);
}

.btn-add-cart-mobile:hover {
    background: var(--dalil-primary-dark);
}

.btn-wishlist-mobile {
    width: 44px;
    height: 44px;
    background: var(--dalil-light);
    border: 1px solid var(--dalil-border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--dalil-transition);
}

.btn-wishlist-mobile:hover,
.btn-wishlist-mobile.active {
    background: var(--dalil-danger);
    color: white;
    border-color: var(--dalil-danger);
}

/* ===================================
   Vehicle Parts Finder Mobile
   =================================== */

.vpf-mobile-enhanced {
    background: linear-gradient(135deg, var(--dalil-primary) 0%, var(--dalil-primary-light) 100%);
    color: white;
    padding: 24px 16px;
    margin: 16px;
    border-radius: var(--dalil-radius);
    box-shadow: var(--dalil-shadow);
}

.vpf-title-mobile {
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
}

.vpf-form-mobile {
    display: grid;
    gap: 16px;
}

.vpf-select-mobile {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    color: white;
    font-size: 16px;
    backdrop-filter: blur(10px);
}

.vpf-select-mobile::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.vpf-select-mobile:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
}

.vpf-search-btn-mobile {
    background: white;
    color: var(--dalil-primary);
    border: none;
    padding: 16px;
    border-radius: 8px;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: var(--dalil-transition);
    margin-top: 8px;
}

.vpf-search-btn-mobile:hover {
    background: var(--dalil-light);
    transform: translateY(-2px);
}

/* ===================================
   Cart Mobile Enhancements
   =================================== */

.cart-item-mobile {
    background: var(--dalil-white);
    border-radius: var(--dalil-radius);
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: var(--dalil-shadow);
    display: flex;
    gap: 16px;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dalil-dark);
    margin-bottom: 8px;
    line-height: 1.3;
}

.cart-item-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--dalil-primary);
    margin-bottom: 12px;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.quantity-control {
    display: flex;
    align-items: center;
    background: var(--dalil-light);
    border-radius: 8px;
    overflow: hidden;
}

.quantity-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: var(--dalil-transition);
}

.quantity-btn:hover {
    background: var(--dalil-border);
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: 600;
    padding: 8px 4px;
}

.remove-item-btn {
    color: var(--dalil-danger);
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: var(--dalil-transition);
}

.remove-item-btn:hover {
    background: rgba(220, 53, 69, 0.1);
}

/* ===================================
   Responsive Utilities
   =================================== */

@media (max-width: 576px) {
    .mobile-only {
        display: block !important;
    }
    
    .desktop-only {
        display: none !important;
    }
    
    .container-mobile {
        padding: 0 16px;
    }
    
    .text-mobile-center {
        text-align: center;
    }
    
    .mb-mobile-3 {
        margin-bottom: 1rem;
    }
    
    .p-mobile-3 {
        padding: 1rem;
    }
}

/* ===================================
   Loading States
   =================================== */

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===================================
   Safe Area Support
   =================================== */

.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
    padding-left: env(safe-area-inset-left);
}

.safe-area-right {
    padding-right: env(safe-area-inset-right);
}

/* ===================================
   Toast Notifications
   =================================== */

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--dalil-white);
    color: var(--dalil-dark);
    padding: 16px 20px;
    border-radius: var(--dalil-radius);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 300px;
    font-weight: 500;
    border-left: 4px solid var(--dalil-primary);
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.toast-success {
    border-left-color: var(--dalil-success);
    background: #f8fff9;
    color: #155724;
}

.toast.toast-warning {
    border-left-color: var(--dalil-warning);
    background: #fffbf0;
    color: #856404;
}

.toast.toast-danger {
    border-left-color: var(--dalil-danger);
    background: #fff5f5;
    color: #721c24;
}

.toast.toast-info {
    border-left-color: #17a2b8;
    background: #f0f9ff;
    color: #0c5460;
}

/* ===================================
   PWA Install Button
   =================================== */

.pwa-install-btn {
    position: fixed;
    bottom: 80px;
    right: 20px;
    background: var(--dalil-primary);
    color: white;
    border: none;
    padding: 16px 20px;
    border-radius: 25px;
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(12, 85, 170, 0.3);
    z-index: 1000;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.pwa-install-btn:hover {
    background: var(--dalil-primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(12, 85, 170, 0.4);
}

/* ===================================
   Connection Status
   =================================== */

.connection-status {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    backdrop-filter: blur(10px);
    transition: var(--dalil-transition);
}

.connection-status.online {
    background: rgba(40, 167, 69, 0.9);
}

.connection-status.offline {
    background: rgba(220, 53, 69, 0.9);
}

/* ===================================
   Keyboard Open State
   =================================== */

.keyboard-open .mobile-nav-enhanced {
    display: none;
}

.keyboard-open .pwa-install-btn {
    display: none;
}

/* ===================================
   Enhanced Animations
   =================================== */

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-slide-up {
    animation: slideInUp 0.3s ease-out;
}

.animate-slide-down {
    animation: slideInDown 0.3s ease-out;
}

.animate-fade-scale {
    animation: fadeInScale 0.3s ease-out;
}

/* ===================================
   Pull to Refresh
   =================================== */

.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-to-refresh::before {
    content: '↓ اسحب للتحديث';
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dalil-primary);
    color: white;
    padding: 10px 20px;
    border-radius: 0 0 15px 15px;
    font-size: 14px;
    font-weight: 500;
    transition: var(--dalil-transition);
    z-index: 1000;
}

.pull-to-refresh.pulling::before {
    top: 0;
    content: '↑ اتركه للتحديث';
}

.pull-to-refresh.refreshing::before {
    content: '🔄 جاري التحديث...';
    animation: pulse 1s infinite;
}

/* ===================================
   Swipe Indicators
   =================================== */

.swipe-indicator {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 20px;
    border-radius: 50%;
    font-size: 24px;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.swipe-indicator.left {
    left: 20px;
}

.swipe-indicator.right {
    right: 20px;
}

.swipe-indicator.show {
    opacity: 1;
}

/* ===================================
   Voice Search
   =================================== */

.voice-search-btn {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--dalil-primary);
    color: white;
    border: none;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--dalil-transition);
}

.voice-search-btn:hover {
    background: var(--dalil-primary-dark);
}

.voice-search-btn.listening {
    background: var(--dalil-danger);
    animation: pulse 1s infinite;
}

.voice-search-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.voice-search-modal.show {
    opacity: 1;
    visibility: visible;
}

.voice-search-content {
    background: white;
    padding: 40px;
    border-radius: var(--dalil-radius);
    text-align: center;
    max-width: 300px;
    width: 90%;
}

.voice-search-icon {
    font-size: 60px;
    color: var(--dalil-primary);
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

/* ===================================
   QR Scanner
   =================================== */

.qr-scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: black;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.qr-scanner-modal.show {
    opacity: 1;
    visibility: visible;
}

.qr-scanner-header {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.qr-scanner-close {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.qr-scanner-video {
    flex: 1;
    width: 100%;
    object-fit: cover;
}

.qr-scanner-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    border: 2px solid white;
    border-radius: 20px;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
}

.qr-scanner-corners {
    position: absolute;
    width: 100%;
    height: 100%;
}

.qr-scanner-corners::before,
.qr-scanner-corners::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid var(--dalil-primary);
}

.qr-scanner-corners::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.qr-scanner-corners::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}
