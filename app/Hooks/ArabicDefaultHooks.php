<?php

namespace App\Hooks;

use App\Helpers\ArabicLanguageHelper;
use Botble\Language\Facades\Language;
use Botble\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Illuminate\Http\Request;

class ArabicDefaultHooks
{
    /**
     * تحديد اللغة الافتراضية عند إنشاء منتج جديد
     */
    public static function setDefaultLanguageForNewProduct(): void
    {
        // إضافة فلتر لتحديد اللغة الافتراضية
        add_filter('language_advanced_current_admin_locale', function ($locale) {
            // إذا لم تكن هناك لغة محددة، استخدم العربية
            if (empty($locale) && is_in_admin(true)) {
                return 'ar';
            }
            return $locale;
        });
        
        // إضافة فلتر لتحديد اللغة عند إنشاء محتوى جديد
        add_filter('language_advanced_default_locale_for_new_content', function () {
            return 'ar';
        });
        
        // تحديد اللغة الافتراضية في نماذج إنشاء المحتوى
        add_action('base_action_form_start', function ($form) {
            if (is_in_admin(true) && method_exists($form, 'getModel')) {
                $model = $form->getModel();
                
                // إذا كان المنتج جديد (لا يملك ID)
                if ($model && !$model->getKey() && LanguageAdvancedManager::isSupported($model)) {
                    // تحديد اللغة الحالية للوحة التحكم كعربية
                    Language::setCurrentAdminLocale('ar');
                }
            }
        });
    }
    
    /**
     * إضافة JavaScript لتحديد اللغة الافتراضية في النماذج
     */
    public static function addDefaultLanguageScript(): void
    {
        add_action('admin_footer', function () {
            if (is_in_admin(true)) {
                echo ArabicLanguageHelper::getArabicJS();
            }
        });
    }
    
    /**
     * تحديد اتجاه النص للعربية
     */
    public static function setRTLForArabic(): void
    {
        add_action('admin_head', function () {
            if (ArabicLanguageHelper::isCurrentLanguageArabic()) {
                echo ArabicLanguageHelper::getArabicCSS();
            }
        });
    }
}
