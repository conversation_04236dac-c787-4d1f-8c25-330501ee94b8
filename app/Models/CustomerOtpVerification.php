<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use Botble\Ecommerce\Models\Customer;

class CustomerOtpVerification extends Model
{
    protected $fillable = [
        'customer_id',
        'phone',
        'email', 
        'otp_code',
        'type',
        'expires_at',
        'verified_at',
        'attempts',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'verified_at' => 'datetime',
    ];

    protected $hidden = [
        'otp_code', // إخفاء الرمز من JSON responses
    ];

    /**
     * العلاقة مع نموذج العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * التحقق من انتهاء صلاحية الرمز
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * التحقق من تأكيد الرمز
     */
    public function isVerified(): bool
    {
        return !is_null($this->verified_at);
    }

    /**
     * التحقق من إمكانية المحاولة
     */
    public function canAttempt(): bool
    {
        return $this->attempts < config('otp.max_attempts', 3);
    }

    /**
     * زيادة عدد المحاولات
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
    }

    /**
     * تحديد الرمز كمؤكد
     */
    public function markAsVerified(): void
    {
        $this->update([
            'verified_at' => now()
        ]);
    }

    /**
     * الحصول على الرمز المفكوك التشفير
     */
    public function getDecryptedCode(): string
    {
        return decrypt($this->otp_code);
    }

    /**
     * تعيين الرمز مع التشفير
     */
    public function setOtpCodeAttribute($value): void
    {
        $this->attributes['otp_code'] = encrypt($value);
    }

    /**
     * التحقق من صحة الرمز
     */
    public function verifyCode(string $inputCode): bool
    {
        if ($this->isExpired()) {
            return false;
        }

        if ($this->isVerified()) {
            return false;
        }

        if (!$this->canAttempt()) {
            return false;
        }

        $this->incrementAttempts();

        if ($this->getDecryptedCode() === $inputCode) {
            $this->markAsVerified();
            return true;
        }

        return false;
    }

    /**
     * البحث عن OTP صالح
     */
    public static function findValidOtp(string $phone, string $type): ?self
    {
        return static::where('phone', $phone)
            ->where('type', $type)
            ->whereNull('verified_at')
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * حذف OTP منتهية الصلاحية
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', now())
            ->delete();
    }

    /**
     * Scope للبحث حسب النوع
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope للبحث حسب الهاتف
     */
    public function scopeForPhone($query, string $phone)
    {
        return $query->where('phone', $phone);
    }

    /**
     * Scope للرموز غير المؤكدة
     */
    public function scopeUnverified($query)
    {
        return $query->whereNull('verified_at');
    }

    /**
     * Scope للرموز الصالحة
     */
    public function scopeValid($query)
    {
        return $query->whereNull('verified_at')
            ->where('expires_at', '>', now());
    }

    /**
     * الحصول على وصف نوع OTP
     */
    public function getTypeDescriptionAttribute(): string
    {
        $types = config('otp.types', []);
        return $types[$this->type] ?? $this->type;
    }

    /**
     * الحصول على الوقت المتبقي بالثواني
     */
    public function getRemainingSecondsAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return $this->expires_at->diffInSeconds(now());
    }

    /**
     * التحقق من إمكانية إعادة الإرسال
     */
    public function canResend(): bool
    {
        // يمكن إعادة الإرسال إذا انتهت الصلاحية أو تم التأكيد
        return $this->isExpired() || $this->isVerified();
    }
}
