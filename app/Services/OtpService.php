<?php

namespace App\Services;

use App\Models\CustomerOtpVerification;
use Botble\Ecommerce\Models\Customer;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class OtpService
{
    protected SmsService $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * توليد وإرسال OTP
     */
    public function generateAndSendOtp(string $phone, string $type, ?int $customerId = null, ?string $email = null): array
    {
        try {
            // التحقق من صحة رقم الهاتف
            if (!$this->smsService->isValidIraqiPhone($phone)) {
                throw new Exception('رقم الهاتف غير صحيح');
            }

            // التحقق من الحد اليومي
            if (!$this->canSendOtp($phone)) {
                throw new Exception('تم تجاوز الحد اليومي لطلبات OTP');
            }

            // التحقق من Rate Limiting
            if (!$this->checkRateLimit($phone)) {
                throw new Exception('يرجى الانتظار قبل طلب رمز جديد');
            }

            // إلغاء أي OTP سابق غير مستخدم
            $this->cancelPreviousOtp($phone, $type);

            // توليد رمز جديد
            $otpCode = $this->generateRandomCode();
            
            // حفظ في قاعدة البيانات
            $otp = CustomerOtpVerification::create([
                'customer_id' => $customerId,
                'phone' => $this->formatPhone($phone),
                'email' => $email,
                'otp_code' => $otpCode, // سيتم تشفيره تلقائياً في النموذج
                'type' => $type,
                'expires_at' => now()->addMinutes(config('otp.expiry_minutes', 5)),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            // إرسال SMS
            $smsResult = $this->smsService->sendOtp($phone, $otpCode, $type);
            
            if (!$smsResult) {
                // حذف OTP إذا فشل الإرسال
                $otp->delete();
                throw new Exception('فشل في إرسال رسالة التأكيد');
            }

            // تسجيل في الكاش للحد اليومي والـ Rate Limiting
            $this->recordOtpRequest($phone);
            $this->setRateLimit($phone);

            // تحديث وقت آخر إرسال للعميل
            if ($customerId) {
                Customer::find($customerId)?->recordOtpSent();
            }

            Log::info('OTP generated and sent successfully', [
                'phone' => $phone,
                'type' => $type,
                'customer_id' => $customerId,
                'otp_id' => $otp->id
            ]);

            return [
                'success' => true,
                'message' => 'تم إرسال رمز التأكيد بنجاح',
                'expires_at' => $otp->expires_at,
                'otp_id' => $otp->id
            ];

        } catch (Exception $e) {
            Log::error('OTP generation failed', [
                'phone' => $phone,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * تأكيد OTP
     */
    public function verifyOtp(string $phone, string $code, string $type): array
    {
        try {
            $phone = $this->formatPhone($phone);
            
            // البحث عن OTP صالح
            $otp = CustomerOtpVerification::findValidOtp($phone, $type);

            if (!$otp) {
                return [
                    'success' => false,
                    'message' => 'رمز التأكيد غير صحيح أو منتهي الصلاحية'
                ];
            }

            // التحقق من الرمز
            if ($otp->verifyCode($code)) {
                // تنفيذ إجراءات إضافية حسب النوع
                $this->handleSuccessfulVerification($otp);

                Log::info('OTP verified successfully', [
                    'phone' => $phone,
                    'type' => $type,
                    'otp_id' => $otp->id
                ]);

                return [
                    'success' => true,
                    'message' => 'تم تأكيد الرمز بنجاح',
                    'customer_id' => $otp->customer_id
                ];
            }

            return [
                'success' => false,
                'message' => 'رمز التأكيد غير صحيح'
            ];

        } catch (Exception $e) {
            Log::error('OTP verification failed', [
                'phone' => $phone,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق من الرمز'
            ];
        }
    }

    /**
     * إعادة إرسال OTP
     */
    public function resendOtp(string $phone, string $type): array
    {
        $phone = $this->formatPhone($phone);
        
        // البحث عن آخر OTP
        $lastOtp = CustomerOtpVerification::where('phone', $phone)
            ->where('type', $type)
            ->latest()
            ->first();

        if ($lastOtp && !$lastOtp->canResend()) {
            return [
                'success' => false,
                'message' => 'لا يمكن إعادة الإرسال حالياً'
            ];
        }

        // إنشاء OTP جديد
        return $this->generateAndSendOtp(
            $phone, 
            $type, 
            $lastOtp?->customer_id, 
            $lastOtp?->email
        );
    }

    /**
     * توليد رمز عشوائي
     */
    protected function generateRandomCode(): string
    {
        $length = config('otp.length', 6);
        return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    /**
     * تنسيق رقم الهاتف
     */
    protected function formatPhone(string $phone): string
    {
        return $this->smsService->formatPhoneNumber($phone);
    }

    /**
     * إلغاء OTP سابق
     */
    protected function cancelPreviousOtp(string $phone, string $type): void
    {
        CustomerOtpVerification::where('phone', $phone)
            ->where('type', $type)
            ->whereNull('verified_at')
            ->delete();
    }

    /**
     * التحقق من الحد اليومي
     */
    protected function canSendOtp(string $phone): bool
    {
        $key = config('otp.cache.daily_limit_prefix') . $phone . ':' . now()->format('Y-m-d');
        $count = Cache::get($key, 0);
        return $count < config('otp.daily_limit', 5);
    }

    /**
     * تسجيل طلب OTP
     */
    protected function recordOtpRequest(string $phone): void
    {
        $key = config('otp.cache.daily_limit_prefix') . $phone . ':' . now()->format('Y-m-d');
        Cache::increment($key);
        Cache::put($key, Cache::get($key), now()->endOfDay());
    }

    /**
     * التحقق من Rate Limiting
     */
    protected function checkRateLimit(string $phone): bool
    {
        $key = config('otp.cache.rate_limit_prefix') . $phone;
        return !Cache::has($key);
    }

    /**
     * تعيين Rate Limiting
     */
    protected function setRateLimit(string $phone): void
    {
        $key = config('otp.cache.rate_limit_prefix') . $phone;
        Cache::put($key, true, now()->addMinute()); // دقيقة واحدة
    }

    /**
     * معالجة التأكيد الناجح
     */
    protected function handleSuccessfulVerification(CustomerOtpVerification $otp): void
    {
        switch ($otp->type) {
            case 'registration':
            case 'phone_verification':
                // تأكيد رقم الهاتف للعميل
                if ($otp->customer) {
                    $otp->customer->markPhoneAsVerified();
                }
                break;
                
            case 'login':
                // يمكن إضافة منطق إضافي لتسجيل الدخول
                break;
                
            case 'password_reset':
                // يمكن إضافة منطق إضافي لإعادة تعيين كلمة المرور
                break;
        }
    }

    /**
     * تنظيف OTP منتهية الصلاحية
     */
    public function cleanupExpiredOtp(): int
    {
        return CustomerOtpVerification::cleanupExpired();
    }

    /**
     * الحصول على إحصائيات OTP
     */
    public function getOtpStats(string $phone = null): array
    {
        $query = CustomerOtpVerification::query();
        
        if ($phone) {
            $query->where('phone', $this->formatPhone($phone));
        }

        return [
            'total_sent' => $query->count(),
            'verified' => $query->whereNotNull('verified_at')->count(),
            'expired' => $query->where('expires_at', '<', now())->whereNull('verified_at')->count(),
            'pending' => $query->where('expires_at', '>', now())->whereNull('verified_at')->count(),
        ];
    }
}
