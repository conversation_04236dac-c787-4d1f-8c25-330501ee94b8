<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class SmsService
{
    protected string $provider;
    protected array $config;

    public function __construct()
    {
        $this->provider = config('otp.sms.default', 'bulksmsiraq');
        $this->config = config("otp.sms.providers.{$this->provider}", []);
    }

    /**
     * إرسال رسالة SMS
     */
    public function sendSms(string $phone, string $message): bool
    {
        try {
            // تنظيف رقم الهاتف
            $phone = $this->formatPhoneNumber($phone);
            
            // محاولة الإرسال مع المزود الأساسي
            $result = $this->sendWithProvider($this->provider, $phone, $message);
            
            if (!$result) {
                // محاولة مع المزود الاحتياطي
                Log::warning("Failed to send SMS with primary provider: {$this->provider}");
                $result = $this->sendWithBackupProvider($phone, $message);
            }

            return $result;
        } catch (Exception $e) {
            Log::error('SMS sending failed: ' . $e->getMessage(), [
                'phone' => $phone,
                'provider' => $this->provider,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * إرسال OTP
     */
    public function sendOtp(string $phone, string $code, string $type): bool
    {
        $template = config("otp.templates.{$type}", 'رمز التأكيد: {code}');
        $message = str_replace(['{code}', '{minutes}'], [$code, config('otp.expiry_minutes', 5)], $template);
        
        return $this->sendSms($phone, $message);
    }

    /**
     * الإرسال مع مزود محدد
     */
    protected function sendWithProvider(string $provider, string $phone, string $message): bool
    {
        switch ($provider) {
            case 'bulksmsiraq':
                return $this->sendWithBulkSMSIraq($phone, $message);
            case 'twilio':
                return $this->sendWithTwilio($phone, $message);
            default:
                throw new Exception("Unsupported SMS provider: {$provider}");
        }
    }

    /**
     * الإرسال مع BulkSMSIraq.com
     */
    protected function sendWithBulkSMSIraq(string $phone, string $message): bool
    {
        $config = config('otp.sms.providers.bulksmsiraq');
        
        if (empty($config['api_key']) || empty($config['api_url'])) {
            throw new Exception('BulkSMSIraq configuration is incomplete');
        }

        $response = Http::timeout(30)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $config['api_key'],
            ])
            ->post($config['api_url'], [
                'to' => $phone,
                'message' => $message,
                'from' => $config['from_name'] ?? 'Dallilak Auto',
                'sender_id' => $config['from_number'] ?? null,
            ]);

        if ($response->successful()) {
            $data = $response->json();
            
            // تسجيل النجاح
            Log::info('SMS sent successfully via BulkSMSIraq', [
                'phone' => $phone,
                'response' => $data
            ]);
            
            return true;
        }

        // تسجيل الفشل
        Log::error('BulkSMSIraq API error', [
            'phone' => $phone,
            'status' => $response->status(),
            'response' => $response->body()
        ]);

        return false;
    }

    /**
     * الإرسال مع Twilio (احتياطي)
     */
    protected function sendWithTwilio(string $phone, string $message): bool
    {
        $config = config('otp.sms.providers.twilio');
        
        if (empty($config['sid']) || empty($config['token'])) {
            throw new Exception('Twilio configuration is incomplete');
        }

        try {
            $response = Http::withBasicAuth($config['sid'], $config['token'])
                ->asForm()
                ->post("https://api.twilio.com/2010-04-01/Accounts/{$config['sid']}/Messages.json", [
                    'To' => $phone,
                    'From' => $config['from'],
                    'Body' => $message,
                ]);

            if ($response->successful()) {
                Log::info('SMS sent successfully via Twilio', [
                    'phone' => $phone,
                    'response' => $response->json()
                ]);
                return true;
            }

            Log::error('Twilio API error', [
                'phone' => $phone,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('Twilio sending failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الإرسال مع المزود الاحتياطي
     */
    protected function sendWithBackupProvider(string $phone, string $message): bool
    {
        $backupProvider = config('otp.sms.backup_provider', 'twilio');
        
        if ($backupProvider === $this->provider) {
            return false; // تجنب التكرار
        }

        return $this->sendWithProvider($backupProvider, $phone, $message);
    }

    /**
     * تنسيق رقم الهاتف
     */
    public function formatPhoneNumber(string $phone): string
    {
        // إزالة المسافات والرموز غير المرغوبة
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // إضافة رمز العراق إذا لم يكن موجوداً
        if (!str_starts_with($phone, '+')) {
            if (str_starts_with($phone, '964')) {
                $phone = '+' . $phone;
            } elseif (str_starts_with($phone, '0')) {
                $phone = '+964' . substr($phone, 1);
            } else {
                $phone = '+964' . $phone;
            }
        }

        return $phone;
    }

    /**
     * التحقق من صحة رقم الهاتف العراقي
     */
    public function isValidIraqiPhone(string $phone): bool
    {
        $phone = $this->formatPhoneNumber($phone);
        
        // أرقام الهواتف العراقية تبدأ بـ +964 ثم رقم من 9-10 أرقام
        return preg_match('/^\+964[0-9]{9,10}$/', $phone);
    }

    /**
     * الحصول على حالة المزود
     */
    public function getProviderStatus(): array
    {
        return [
            'current_provider' => $this->provider,
            'config_valid' => !empty($this->config),
            'api_key_set' => !empty($this->config['api_key'] ?? null),
        ];
    }

    /**
     * اختبار الاتصال مع المزود
     */
    public function testConnection(): bool
    {
        try {
            // إرسال رسالة اختبار لرقم وهمي
            $testPhone = '+964750000000';
            $testMessage = 'Test message from Dallilak Auto';
            
            // هنا يمكن إضافة endpoint خاص للاختبار إذا كان متوفراً
            return true; // مؤقتاً
        } catch (Exception $e) {
            Log::error('SMS provider connection test failed: ' . $e->getMessage());
            return false;
        }
    }
}
