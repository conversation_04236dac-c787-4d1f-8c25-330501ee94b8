<?php

namespace App\Helpers;

use Botble\Language\Facades\Language;
use Botble\LanguageAdvanced\Supports\LanguageAdvancedManager;

class ArabicLanguageHelper
{
    /**
     * التحقق من أن اللغة الحالية هي العربية
     */
    public static function isCurrentLanguageArabic(): bool
    {
        $currentLocale = Language::getCurrentAdminLocaleCode();
        return $currentLocale === 'ar';
    }
    
    /**
     * تحديد اللغة العربية كافتراضية للوحة التحكم
     */
    public static function setArabicAsDefault(): void
    {
        Language::setCurrentAdminLocale('ar');
        app()->setLocale('ar');
    }
    
    /**
     * الحصول على اللغة الافتراضية للمحتوى الجديد
     */
    public static function getDefaultContentLanguage(): string
    {
        return 'ar';
    }
    
    /**
     * التحقق من أن النموذج يدعم الترجمة
     */
    public static function isTranslatableModel($model): bool
    {
        return LanguageAdvancedManager::isSupported($model);
    }
    
    /**
     * إضافة CSS للدعم الكامل للعربية
     */
    public static function getArabicCSS(): string
    {
        return '
        <style>
            /* دعم اللغة العربية في النماذج */
            .form-control[dir="rtl"], 
            .form-select[dir="rtl"], 
            textarea[dir="rtl"] {
                direction: rtl;
                text-align: right;
            }
            
            /* دعم المحرر النصي */
            .ck-editor__editable {
                direction: rtl;
                text-align: right;
            }
            
            /* دعم حقول الإدخال في المنتجات */
            .product-form .form-control,
            .product-form .form-select,
            .product-form textarea {
                direction: rtl;
                text-align: right;
            }
            
            /* دعم العناوين والتسميات */
            .form-label {
                text-align: right;
            }
            
            /* دعم القوائم المنسدلة */
            .select2-container--default .select2-selection--single .select2-selection__rendered {
                text-align: right;
                direction: rtl;
            }
        </style>';
    }
    
    /**
     * إضافة JavaScript لدعم اللغة العربية
     */
    public static function getArabicJS(): string
    {
        return '
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                // تحديد اللغة العربية كافتراضية
                function setArabicAsDefault() {
                    // حقل اللغة المخفي
                    var languageInput = document.querySelector("input[name=\'language\']");
                    if (languageInput && !languageInput.value) {
                        languageInput.value = "ar";
                    }
                    
                    // قوائم اختيار اللغة
                    var languageSelects = document.querySelectorAll("select[name=\'language\']");
                    languageSelects.forEach(function(select) {
                        if (!select.value) {
                            select.value = "ar";
                        }
                    });
                    
                    // تحديد اتجاه النص للحقول
                    var formControls = document.querySelectorAll(".form-control, .form-select, textarea");
                    formControls.forEach(function(control) {
                        if (!control.hasAttribute("dir")) {
                            control.setAttribute("dir", "rtl");
                        }
                    });
                }
                
                // تنفيذ الإعدادات
                setArabicAsDefault();
                
                // إعادة تطبيق الإعدادات عند تحميل محتوى جديد (AJAX)
                var observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes.length > 0) {
                            setTimeout(setArabicAsDefault, 100);
                        }
                    });
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            });
        </script>';
    }
}
