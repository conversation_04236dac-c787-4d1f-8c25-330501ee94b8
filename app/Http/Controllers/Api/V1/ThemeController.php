<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ThemeController extends Controller
{
    /**
     * Get theme settings for mobile app
     */
    public function getThemeSettings()
    {
        try {
            // Cache theme settings for 1 hour
            $themeSettings = Cache::remember('mobile_theme_settings', 3600, function () {
                return $this->fetchThemeSettings();
            });

            return response()->json([
                'success' => true,
                'data' => $themeSettings,
                'message' => 'Theme settings retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve theme settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific theme setting
     */
    public function getThemeSetting($key)
    {
        try {
            $setting = DB::table('settings')
                ->where('key', $key)
                ->orWhere('key', 'like', "%{$key}%")
                ->first();

            if (!$setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Theme setting not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'key' => $setting->key,
                    'value' => $setting->value
                ],
                'message' => 'Theme setting retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve theme setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Fetch all theme settings from database
     */
    private function fetchThemeSettings()
    {
        // Get all theme-related settings
        $settings = DB::table('settings')
            ->whereIn('key', [
                'theme-shofy-primary_color',
                'theme-shofy-tp_primary_font',
                'theme-shofy-logo',
                'theme-shofy-logo_light',
                'theme-shofy-favicon',
                'theme-shofy-site_name',
                'theme-shofy-site_title',
                'admin_primary_color',
                'admin_secondary_color',
                'admin_text_color',
                'admin_link_color',
                'admin_primary_font',
                'announcement_text_color',
                'announcement_background_color'
            ])
            ->get()
            ->keyBy('key');

        // Structure the theme data for mobile app
        return [
            'colors' => [
                'primary' => $settings['theme-shofy-primary_color']->value ?? '#0C55AA',
                'secondary' => $settings['admin_secondary_color']->value ?? '#6c7a91',
                'text' => $settings['admin_text_color']->value ?? '#182433',
                'link' => $settings['admin_link_color']->value ?? '#206bc4',
                'announcement_text' => $settings['announcement_text_color']->value ?? '#fff',
                'announcement_background' => $settings['announcement_background_color']->value ?? 'transparent',
            ],
            'fonts' => [
                'primary' => $settings['theme-shofy-tp_primary_font']->value ?? 'Roboto',
                'admin' => $settings['admin_primary_font']->value ?? 'Cairo',
            ],
            'branding' => [
                'site_name' => $settings['theme-shofy-site_name']->value ?? 'Shofy',
                'site_title' => $settings['theme-shofy-site_title']->value ?? 'دليلك لقطع غيار السيارات',
                'logo' => $this->getFullImageUrl($settings['theme-shofy-logo']->value ?? ''),
                'logo_light' => $this->getFullImageUrl($settings['theme-shofy-logo_light']->value ?? ''),
                'favicon' => $this->getFullImageUrl($settings['theme-shofy-favicon']->value ?? ''),
            ],
            'mobile_specific' => [
                'primary_color_hex' => $this->hexToInt($settings['theme-shofy-primary_color']->value ?? '#0C55AA'),
                'secondary_color_hex' => $this->hexToInt($settings['admin_secondary_color']->value ?? '#6c7a91'),
                'text_color_hex' => $this->hexToInt($settings['admin_text_color']->value ?? '#182433'),
                'gradient_colors' => [
                    $settings['theme-shofy-primary_color']->value ?? '#0C55AA',
                    $this->lightenColor($settings['theme-shofy-primary_color']->value ?? '#0C55AA', 20)
                ]
            ]
        ];
    }

    /**
     * Get full image URL
     */
    private function getFullImageUrl($imagePath)
    {
        if (empty($imagePath)) {
            return '';
        }

        // If already full URL, return as is
        if (str_starts_with($imagePath, 'http')) {
            return $imagePath;
        }

        // Build full URL
        $baseUrl = rtrim(config('app.url'), '/');
        return $baseUrl . '/storage/' . ltrim($imagePath, '/');
    }

    /**
     * Convert hex color to integer for Flutter
     */
    private function hexToInt($hex)
    {
        // Remove # if present
        $hex = ltrim($hex, '#');
        
        // Convert to integer
        return hexdec('FF' . $hex); // Add FF for alpha channel
    }

    /**
     * Lighten a hex color by percentage
     */
    private function lightenColor($hex, $percent)
    {
        $hex = ltrim($hex, '#');
        
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        $r = min(255, $r + ($percent / 100) * (255 - $r));
        $g = min(255, $g + ($percent / 100) * (255 - $g));
        $b = min(255, $b + ($percent / 100) * (255 - $b));
        
        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    /**
     * Get color palette for mobile app
     */
    public function getColorPalette()
    {
        try {
            $primaryColor = DB::table('settings')
                ->where('key', 'theme-shofy-primary_color')
                ->value('value') ?? '#0C55AA';

            $palette = [
                'primary' => $primaryColor,
                'primary_light' => $this->lightenColor($primaryColor, 20),
                'primary_dark' => $this->lightenColor($primaryColor, -20),
                'secondary' => '#6c7a91',
                'success' => '#28a745',
                'warning' => '#ffc107',
                'danger' => '#dc3545',
                'info' => '#17a2b8',
                'light' => '#f8f9fa',
                'dark' => '#343a40',
                'white' => '#ffffff',
                'black' => '#000000',
                'grey_100' => '#f8f9fa',
                'grey_200' => '#e9ecef',
                'grey_300' => '#dee2e6',
                'grey_400' => '#ced4da',
                'grey_500' => '#adb5bd',
                'grey_600' => '#6c757d',
                'grey_700' => '#495057',
                'grey_800' => '#343a40',
                'grey_900' => '#212529',
            ];

            return response()->json([
                'success' => true,
                'data' => $palette,
                'message' => 'Color palette retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve color palette',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get typography settings
     */
    public function getTypography()
    {
        try {
            $typography = [
                'primary_font' => DB::table('settings')
                    ->where('key', 'theme-shofy-tp_primary_font')
                    ->value('value') ?? 'Roboto',
                'arabic_font' => DB::table('settings')
                    ->where('key', 'admin_primary_font')
                    ->value('value') ?? 'Cairo',
                'font_sizes' => [
                    'xs' => 12,
                    'sm' => 14,
                    'base' => 16,
                    'lg' => 18,
                    'xl' => 20,
                    '2xl' => 24,
                    '3xl' => 30,
                    '4xl' => 36,
                    '5xl' => 48,
                    '6xl' => 60,
                ],
                'font_weights' => [
                    'light' => 300,
                    'normal' => 400,
                    'medium' => 500,
                    'semibold' => 600,
                    'bold' => 700,
                    'extrabold' => 800,
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $typography,
                'message' => 'Typography settings retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve typography settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear theme cache
     */
    public function clearThemeCache()
    {
        try {
            Cache::forget('mobile_theme_settings');
            
            return response()->json([
                'success' => true,
                'message' => 'Theme cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear theme cache',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
