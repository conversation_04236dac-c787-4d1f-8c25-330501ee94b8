<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Services\OtpService;
use Botble\Ecommerce\Models\Customer;
use Botble\Theme\Facades\Theme;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class OtpController extends Controller
{
    protected OtpService $otpService;

    public function __construct(OtpService $otpService)
    {
        $this->otpService = $otpService;
    }

    /**
     * عرض صفحة إدخال OTP
     */
    public function showVerificationForm(Request $request)
    {
        $phone = $request->get('phone');
        $type = $request->get('type', 'registration');
        $email = $request->get('email');

        if (!$phone) {
            abort(404);
        }

        return Theme::scope('ecommerce.customers.otp-verification', [
            'phone' => $phone,
            'type' => $type,
            'email' => $email,
            'typeDescription' => config("otp.types.{$type}", $type)
        ], 'plugins/ecommerce::themes.customers.otp-verification')->render();
    }

    /**
     * إرسال OTP
     */
    public function sendOtp(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => config('otp.validation.phone'),
            'type' => 'required|string|in:registration,login,password_reset,phone_verification,two_factor',
            'customer_id' => 'nullable|integer|exists:ec_customers,id',
            'email' => 'nullable|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'البيانات المدخلة غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->otpService->generateAndSendOtp(
            $request->input('phone'),
            $request->input('type'),
            $request->input('customer_id'),
            $request->input('email')
        );

        return response()->json($result);
    }

    /**
     * تأكيد OTP
     */
    public function verifyOtp(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => config('otp.validation.phone'),
            'code' => config('otp.validation.code'),
            'type' => 'required|string|in:registration,login,password_reset,phone_verification,two_factor'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'البيانات المدخلة غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        $result = $this->otpService->verifyOtp(
            $request->input('phone'),
            $request->input('code'),
            $request->input('type')
        );

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return $this->handleSuccessfulVerification($request, $result);
        }

        return back()->withErrors(['code' => $result['message']])->withInput();
    }

    /**
     * إعادة إرسال OTP
     */
    public function resendOtp(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => config('otp.validation.phone'),
            'type' => 'required|string|in:registration,login,password_reset,phone_verification,two_factor'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'البيانات المدخلة غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->otpService->resendOtp(
            $request->input('phone'),
            $request->input('type')
        );

        return response()->json($result);
    }

    /**
     * تسجيل الدخول باستخدام OTP
     */
    public function loginWithOtp(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => config('otp.validation.phone')
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'رقم الهاتف غير صحيح',
                    'errors' => $validator->errors()
                ], 422);
            }

            return back()->withErrors($validator)->withInput();
        }

        $phone = $request->input('phone');
        $customer = Customer::where('phone', $phone)->first();

        if (!$customer) {
            $message = 'لا يوجد حساب مرتبط بهذا الرقم';
            
            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => $message]);
            }

            return back()->withErrors(['phone' => $message])->withInput();
        }

        $result = $this->otpService->generateAndSendOtp($phone, 'login', $customer->id, $customer->email);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return redirect()->route('customer.otp.verify', [
                'phone' => $phone,
                'type' => 'login'
            ])->with('success', $result['message']);
        }

        return back()->withErrors(['phone' => $result['message']])->withInput();
    }

    /**
     * معالجة التأكيد الناجح
     */
    protected function handleSuccessfulVerification(Request $request, array $result): RedirectResponse
    {
        $type = $request->input('type');
        $customerId = $result['customer_id'] ?? null;

        switch ($type) {
            case 'registration':
                if ($customerId) {
                    $customer = Customer::find($customerId);
                    if ($customer) {
                        // تأكيد الحساب وتسجيل الدخول
                        $customer->update(['confirmed_at' => now()]);
                        Auth::guard('customer')->login($customer);
                        
                        return redirect()->route('customer.overview')
                            ->with('success', 'تم تأكيد حسابك بنجاح!');
                    }
                }
                break;

            case 'login':
                if ($customerId) {
                    $customer = Customer::find($customerId);
                    if ($customer) {
                        Auth::guard('customer')->login($customer);
                        
                        $redirectUrl = session('url.intended', route('customer.overview'));
                        return redirect($redirectUrl)
                            ->with('success', 'تم تسجيل الدخول بنجاح!');
                    }
                }
                break;

            case 'password_reset':
                return redirect()->route('customer.password.reset.form', [
                    'phone' => $request->input('phone'),
                    'verified' => true
                ])->with('success', 'تم تأكيد رقم الهاتف، يمكنك الآن تعيين كلمة مرور جديدة');

            case 'phone_verification':
                return redirect()->route('customer.overview')
                    ->with('success', 'تم تأكيد رقم الهاتف بنجاح!');
        }

        return redirect()->route('customer.login')
            ->with('success', 'تم التأكيد بنجاح!');
    }

    /**
     * إحصائيات OTP (للمطورين)
     */
    public function stats(Request $request): JsonResponse
    {
        if (!app()->environment('local')) {
            abort(404);
        }

        $phone = $request->get('phone');
        $stats = $this->otpService->getOtpStats($phone);

        return response()->json($stats);
    }

    /**
     * تنظيف OTP منتهية الصلاحية
     */
    public function cleanup(): JsonResponse
    {
        if (!app()->environment('local')) {
            abort(404);
        }

        $deleted = $this->otpService->cleanupExpiredOtp();

        return response()->json([
            'success' => true,
            'message' => "تم حذف {$deleted} رمز منتهي الصلاحية"
        ]);
    }
}
