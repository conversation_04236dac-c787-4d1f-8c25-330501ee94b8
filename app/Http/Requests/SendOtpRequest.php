<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SendOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'phone' => config('otp.validation.phone'),
            'type' => 'required|string|in:registration,login,password_reset,phone_verification,two_factor',
            'customer_id' => 'nullable|integer|exists:ec_customers,id',
            'email' => 'nullable|email'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'phone.required' => 'رقم الهاتف مطلوب',
            'phone.regex' => 'رقم الهاتف غير صحيح. يجب أن يكون رقم عراقي صحيح',
            'type.required' => 'نوع التأكيد مطلوب',
            'type.in' => 'نوع التأكيد غير صحيح',
            'customer_id.exists' => 'العميل غير موجود',
            'email.email' => 'البريد الإلكتروني غير صحيح'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'phone' => 'رقم الهاتف',
            'type' => 'نوع التأكيد',
            'customer_id' => 'معرف العميل',
            'email' => 'البريد الإلكتروني'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // تنظيف رقم الهاتف
        if ($this->has('phone')) {
            $phone = preg_replace('/[^\d+]/', '', $this->phone);
            
            // إضافة رمز العراق إذا لم يكن موجوداً
            if (!str_starts_with($phone, '+')) {
                if (str_starts_with($phone, '964')) {
                    $phone = '+' . $phone;
                } elseif (str_starts_with($phone, '0')) {
                    $phone = '+964' . substr($phone, 1);
                } else {
                    $phone = '+964' . $phone;
                }
            }
            
            $this->merge(['phone' => $phone]);
        }
    }
}
