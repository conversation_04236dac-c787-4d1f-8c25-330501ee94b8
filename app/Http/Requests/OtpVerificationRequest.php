<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OtpVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'phone' => config('otp.validation.phone'),
            'code' => config('otp.validation.code'),
            'type' => 'required|string|in:registration,login,password_reset,phone_verification,two_factor'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'phone.required' => 'رقم الهاتف مطلوب',
            'phone.regex' => 'رقم الهاتف غير صحيح',
            'code.required' => 'رمز التأكيد مطلوب',
            'code.size' => 'رمز التأكيد يجب أن يكون 6 أرقام',
            'code.regex' => 'رمز التأكيد يجب أن يحتوي على أرقام فقط',
            'type.required' => 'نوع التأكيد مطلوب',
            'type.in' => 'نوع التأكيد غير صحيح'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'phone' => 'رقم الهاتف',
            'code' => 'رمز التأكيد',
            'type' => 'نوع التأكيد'
        ];
    }
}
