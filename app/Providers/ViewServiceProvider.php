<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // إصلاح مشكلة laravel-exceptions-renderer
        $this->registerExceptionRendererViews();
    }

    /**
     * تسجيل views الخاصة بـ exception renderer
     */
    protected function registerExceptionRendererViews(): void
    {
        // إضافة namespace بشكل مباشر
        $paths = [
            resource_path('views/errors'),
            resource_path('views'),
        ];

        // إضافة مسار Laravel الافتراضي إذا كان موجوداً
        $laravelExceptionPath = base_path('vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/views');
        if (is_dir($laravelExceptionPath)) {
            $paths[] = $laravelExceptionPath;
        }

        View::addNamespace('laravel-exceptions-renderer', $paths);
    }
}
