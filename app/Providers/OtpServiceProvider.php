<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\OtpService;
use App\Services\SmsService;

class OtpServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // تسجيل خدمة SMS
        $this->app->singleton(SmsService::class, function ($app) {
            return new SmsService();
        });

        // تسجيل خدمة OTP
        $this->app->singleton(OtpService::class, function ($app) {
            return new OtpService($app->make(SmsService::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحميل ملف الإعدادات
        $this->mergeConfigFrom(
            config_path('otp.php'), 'otp'
        );

        // تسجيل Commands إذا كان في وضع console
        if ($this->app->runningInConsole()) {
            $this->commands([
                // يمكن إضافة commands هنا لاحقاً
            ]);
        }
    }
}
