<?php

namespace App\Providers;

use App\Hooks\ArabicDefaultHooks;
use Botble\Language\Facades\Language;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;

class ArabicDefaultServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تحديد اللغة الافتراضية للوحة التحكم
        if (is_in_admin(true)) {
            $this->setAdminDefaultLanguage();
        }

        // إضافة فلتر لتحديد اللغة الافتراضية عند إنشاء محتوى جديد
        add_filter('language_advanced_default_locale', [$this, 'getDefaultContentLanguage']);

        // إضافة فلتر لترتيب اللغات
        add_filter('language_get_active_languages', [$this, 'prioritizeArabicLanguage']);

        // تفعيل الهوكس المخصصة
        ArabicDefaultHooks::setDefaultLanguageForNewProduct();
        ArabicDefaultHooks::addDefaultLanguageScript();
        ArabicDefaultHooks::setRTLForArabic();
    }
    
    /**
     * تحديد اللغة الافتراضية للوحة التحكم
     */
    protected function setAdminDefaultLanguage(): void
    {
        // التحقق من وجود اللغة العربية كافتراضية في قاعدة البيانات
        $defaultLanguage = Language::getDefaultLanguage(['lang_code', 'lang_locale']);
        
        if ($defaultLanguage && $defaultLanguage->lang_code === 'ar') {
            // تحديد اللغة الحالية للوحة التحكم
            Language::setCurrentAdminLocale('ar');
            App::setLocale('ar');
        }
    }
    
    /**
     * تحديد اللغة الافتراضية للمحتوى الجديد
     */
    public function getDefaultContentLanguage(): string
    {
        return config('arabic_default.default_content_language', 'ar');
    }
    
    /**
     * ترتيب اللغات بحيث تكون العربية أولاً
     */
    public function prioritizeArabicLanguage($languages)
    {
        if (is_array($languages) || $languages instanceof \Illuminate\Support\Collection) {
            // ترتيب اللغات بحيث تكون العربية أولاً
            $sorted = collect($languages)->sortBy(function ($language) {
                if (isset($language['lang_code']) && $language['lang_code'] === 'ar') {
                    return 0;
                }
                if (isset($language->lang_code) && $language->lang_code === 'ar') {
                    return 0;
                }
                return 1;
            });
            
            return $sorted;
        }
        
        return $languages;
    }
}
