{{-- PWA Integration Includes for Dalil Auto Parts --}}

{{-- <PERSON><PERSON> Manifest and Meta Tags --}}
<link rel="manifest" href="/pwa/manifest.json">
<meta name="theme-color" content="#0C55AA">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="دليل قطع الغيار">
<meta name="msapplication-TileColor" content="#0C55AA">
<meta name="msapplication-config" content="/pwa/browserconfig.xml">

{{-- Apple Touch Icons --}}
<link rel="apple-touch-icon" sizes="72x72" href="/pwa/icon-72x72.png">
<link rel="apple-touch-icon" sizes="96x96" href="/pwa/icon-96x96.png">
<link rel="apple-touch-icon" sizes="128x128" href="/pwa/icon-128x128.png">
<link rel="apple-touch-icon" sizes="144x144" href="/pwa/icon-144x144.png">
<link rel="apple-touch-icon" sizes="152x152" href="/pwa/icon-152x152.png">
<link rel="apple-touch-icon" sizes="192x192" href="/pwa/icon-192x192.png">
<link rel="apple-touch-icon" sizes="384x384" href="/pwa/icon-384x384.png">
<link rel="apple-touch-icon" sizes="512x512" href="/pwa/icon-512x512.png">

{{-- Favicon --}}
<link rel="icon" type="image/png" sizes="32x32" href="/pwa/icon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/pwa/icon-16x16.png">

{{-- PWA CSS Enhancements --}}
<link rel="stylesheet" href="/css/mobile-pwa-enhancements.css">

{{-- PWA JavaScript --}}
<script>
    // PWA Configuration
    window.pwaConfig = {
        apiBase: '{{ url("/api/v1") }}',
        baseUrl: '{{ url("/") }}',
        csrfToken: '{{ csrf_token() }}',
        locale: '{{ app()->getLocale() }}',
        isRTL: {{ app()->getLocale() === 'ar' ? 'true' : 'false' }},
        user: @auth('customer') {!! json_encode([
            'id' => auth('customer')->id(),
            'name' => auth('customer')->user()->name,
            'email' => auth('customer')->user()->email,
            'is_wholesale' => auth('customer')->user()->is_wholesale ?? false
        ]) !!} @else null @endauth,
        vapidPublicKey: '{{ config("services.vapid.public_key", "") }}'
    };

    // Service Worker Registration
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/service-worker.js')
                .then(function(registration) {
                    console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    
                    // Check for updates
                    registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', function() {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New content is available, show update notification
                                showUpdateNotification();
                            }
                        });
                    });
                })
                .catch(function(err) {
                    console.log('ServiceWorker registration failed: ', err);
                });
        });
    }

    // Show update notification
    function showUpdateNotification() {
        if (window.dalilPWA) {
            window.dalilPWA.showToast('تحديث جديد متاح - سيتم التحديث تلقائياً', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
    }

    // PWA Install Prompt
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        
        // Show install button after 3 seconds
        setTimeout(() => {
            if (deferredPrompt && !window.matchMedia('(display-mode: standalone)').matches) {
                showInstallPrompt();
            }
        }, 3000);
    });

    function showInstallPrompt() {
        const installBanner = document.createElement('div');
        installBanner.className = 'pwa-install-banner';
        installBanner.innerHTML = `
            <div class="install-banner-content">
                <div class="install-banner-icon">📱</div>
                <div class="install-banner-text">
                    <strong>تثبيت التطبيق</strong>
                    <p>احصل على تجربة أفضل مع تطبيق دليل قطع الغيار</p>
                </div>
                <div class="install-banner-actions">
                    <button onclick="installPWA()" class="btn-install">تثبيت</button>
                    <button onclick="dismissInstallPrompt()" class="btn-dismiss">×</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(installBanner);
        setTimeout(() => installBanner.classList.add('show'), 100);
    }

    async function installPWA() {
        if (deferredPrompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
                gtag && gtag('event', 'pwa_install_accepted');
            } else {
                console.log('User dismissed the install prompt');
                gtag && gtag('event', 'pwa_install_dismissed');
            }
            
            deferredPrompt = null;
            dismissInstallPrompt();
        }
    }

    function dismissInstallPrompt() {
        const banner = document.querySelector('.pwa-install-banner');
        if (banner) {
            banner.classList.remove('show');
            setTimeout(() => banner.remove(), 300);
        }
    }

    // iOS Install Instructions
    function showIOSInstallInstructions() {
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.navigator.standalone) {
            const iosPrompt = document.createElement('div');
            iosPrompt.className = 'ios-install-prompt';
            iosPrompt.innerHTML = `
                <div class="ios-prompt-content">
                    <h3>تثبيت التطبيق على iOS</h3>
                    <p>1. اضغط على زر المشاركة <span style="font-size: 18px;">⬆️</span></p>
                    <p>2. اختر "إضافة إلى الشاشة الرئيسية"</p>
                    <p>3. اضغط "إضافة"</p>
                    <button onclick="this.closest('.ios-install-prompt').remove()">فهمت</button>
                </div>
            `;
            
            document.body.appendChild(iosPrompt);
            setTimeout(() => iosPrompt.classList.add('show'), 100);
        }
    }

    // Check if running as PWA
    function isPWA() {
        return window.matchMedia('(display-mode: standalone)').matches || 
               window.navigator.standalone === true;
    }

    // PWA Analytics
    if (isPWA()) {
        gtag && gtag('event', 'pwa_launch');
        document.body.classList.add('pwa-mode');
    }
</script>

{{-- PWA Styles --}}
<style>
    /* PWA Install Banner */
    .pwa-install-banner {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
        color: white;
        padding: 16px;
        z-index: 10000;
        transform: translateY(100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    }

    .pwa-install-banner.show {
        transform: translateY(0);
    }

    .install-banner-content {
        display: flex;
        align-items: center;
        gap: 16px;
        max-width: 500px;
        margin: 0 auto;
    }

    .install-banner-icon {
        font-size: 32px;
        flex-shrink: 0;
    }

    .install-banner-text {
        flex: 1;
    }

    .install-banner-text strong {
        display: block;
        font-size: 16px;
        margin-bottom: 4px;
    }

    .install-banner-text p {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
    }

    .install-banner-actions {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
    }

    .btn-install {
        background: white;
        color: #0C55AA;
        border: none;
        padding: 10px 16px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-install:hover {
        background: #f0f0f0;
        transform: translateY(-1px);
    }

    .btn-dismiss {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: all 0.3s ease;
    }

    .btn-dismiss:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    /* iOS Install Prompt */
    .ios-install-prompt {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        padding: 20px;
    }

    .ios-install-prompt.show {
        opacity: 1;
        visibility: visible;
    }

    .ios-prompt-content {
        background: white;
        border-radius: 16px;
        padding: 30px;
        max-width: 350px;
        text-align: center;
        color: #333;
    }

    .ios-prompt-content h3 {
        margin: 0 0 20px 0;
        color: #0C55AA;
        font-size: 20px;
    }

    .ios-prompt-content p {
        margin: 12px 0;
        line-height: 1.5;
        text-align: right;
    }

    .ios-prompt-content button {
        background: #0C55AA;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        margin-top: 20px;
        transition: all 0.3s ease;
    }

    .ios-prompt-content button:hover {
        background: #094488;
    }

    /* PWA Mode Adjustments */
    .pwa-mode {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .pwa-mode .mobile-nav-enhanced {
        padding-bottom: calc(8px + env(safe-area-inset-bottom));
    }

    /* Hide elements in PWA mode */
    .pwa-mode .browser-only {
        display: none !important;
    }

    /* Show elements only in PWA mode */
    .pwa-only {
        display: none !important;
    }

    .pwa-mode .pwa-only {
        display: block !important;
    }

    /* Responsive adjustments */
    @media (max-width: 480px) {
        .install-banner-content {
            flex-direction: column;
            text-align: center;
            gap: 12px;
        }

        .install-banner-actions {
            width: 100%;
            justify-content: center;
        }

        .btn-install {
            flex: 1;
            max-width: 200px;
        }
    }
</style>

{{-- Load PWA JavaScript Files --}}
<script src="/js/mobile-pwa-features.js" defer></script>
<script src="/js/advanced-mobile-features.js" defer></script>
<script src="/js/pwa-integration.js" defer></script>

{{-- Initialize PWA Features --}}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show iOS install instructions on first visit
        if (!localStorage.getItem('ios_install_shown') && /iPad|iPhone|iPod/.test(navigator.userAgent)) {
            setTimeout(() => {
                showIOSInstallInstructions();
                localStorage.setItem('ios_install_shown', 'true');
            }, 5000);
        }

        // Track PWA usage
        if (isPWA()) {
            // Track time spent in PWA
            let startTime = Date.now();
            window.addEventListener('beforeunload', () => {
                const timeSpent = Math.round((Date.now() - startTime) / 1000);
                gtag && gtag('event', 'pwa_session_duration', {
                    value: timeSpent
                });
            });
        }

        // Add PWA-specific event listeners
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                gtag && gtag('event', 'pwa_background');
            } else {
                gtag && gtag('event', 'pwa_foreground');
            }
        });
    });
</script>

{{-- Structured Data for PWA --}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "MobileApplication",
    "name": "دليل قطع الغيار",
    "description": "دليلك الشامل لقطع غيار السيارات - أفضل الأسعار وأسرع التوصيل",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "All",
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
    },
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "1250"
    }
}
</script>
