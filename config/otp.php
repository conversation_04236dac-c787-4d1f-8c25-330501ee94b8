<?php

return [
    /*
    |--------------------------------------------------------------------------
    | OTP Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for OTP (One Time Password) system
    | used for customer authentication and verification.
    |
    */

    'enabled' => env('OTP_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | OTP Settings
    |--------------------------------------------------------------------------
    */
    'length' => (int) env('OTP_LENGTH', 6),
    'expiry_minutes' => (int) env('OTP_EXPIRY_MINUTES', 5),
    'max_attempts' => (int) env('OTP_MAX_ATTEMPTS', 3),
    'daily_limit' => (int) env('OTP_DAILY_LIMIT', 5),

    /*
    |--------------------------------------------------------------------------
    | SMS Provider Configuration
    |--------------------------------------------------------------------------
    */
    'sms' => [
        'default' => env('SMS_PROVIDER', 'bulksmsiraq'),
        
        'providers' => [
            'bulksmsiraq' => [
                'api_url' => env('SMS_API_URL', 'https://bulksmsiraq.com/api/v1/send'),
                'api_key' => env('SMS_API_KEY'),
                'api_secret' => env('SMS_API_SECRET'),
                'from_name' => env('SMS_FROM_NAME', 'Dallilak Auto'),
                'from_number' => env('SMS_FROM_NUMBER'),
            ],
            
            'twilio' => [
                'sid' => env('BACKUP_SMS_SID'),
                'token' => env('BACKUP_SMS_TOKEN'),
                'from' => env('BACKUP_SMS_FROM'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | OTP Types
    |--------------------------------------------------------------------------
    */
    'types' => [
        'registration' => 'تأكيد التسجيل',
        'login' => 'تسجيل الدخول',
        'password_reset' => 'إعادة تعيين كلمة المرور',
        'phone_verification' => 'تأكيد رقم الهاتف',
        'two_factor' => 'المصادقة الثنائية',
    ],

    /*
    |--------------------------------------------------------------------------
    | SMS Templates
    |--------------------------------------------------------------------------
    */
    'templates' => [
        'registration' => 'رمز التأكيد لتسجيل حسابك في دليلك أوتو: {code}. صالح لمدة {minutes} دقائق.',
        'login' => 'رمز تسجيل الدخول لحسابك في دليلك أوتو: {code}. صالح لمدة {minutes} دقائق.',
        'password_reset' => 'رمز إعادة تعيين كلمة المرور: {code}. صالح لمدة {minutes} دقائق.',
        'phone_verification' => 'رمز تأكيد رقم الهاتف: {code}. صالح لمدة {minutes} دقائق.',
        'two_factor' => 'رمز المصادقة الثنائية: {code}. صالح لمدة {minutes} دقائق.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_codes' => true,
        'rate_limiting' => true,
        'ip_blocking' => true,
        'log_attempts' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'prefix' => 'otp:',
        'daily_limit_prefix' => 'otp_daily_limit:',
        'rate_limit_prefix' => 'otp_rate_limit:',
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'phone' => 'required|string|regex:/^(\+964|964|0)?[0-9]{10}$/',
        'code' => 'required|string|size:6|regex:/^[0-9]{6}$/',
    ],
];
