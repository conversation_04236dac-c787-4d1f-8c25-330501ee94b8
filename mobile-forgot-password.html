<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>نسيت كلمة المرور - دليل قطع الغيار</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0C55AA">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #333;
        }
        
        .forgot-container {
            background: white;
            border-radius: 24px;
            padding: 40px 30px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .forgot-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0C55AA, #3D73C4, #0C55AA);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .icon-section {
            margin-bottom: 30px;
        }
        
        .forgot-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(12, 85, 170, 0.3);
        }
        
        .forgot-icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #0C55AA;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 24px;
            position: relative;
            text-align: right;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
            text-align: right;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #0C55AA;
            background: white;
            box-shadow: 0 0 0 4px rgba(12, 85, 170, 0.1);
        }
        
        .form-input.error {
            border-color: #dc3545;
            background: #fff5f5;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 6px;
            display: none;
            text-align: right;
        }
        
        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #0C55AA 0%, #3D73C4 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(12, 85, 170, 0.4);
        }
        
        .submit-btn:active {
            transform: translateY(0);
        }
        
        .submit-btn.loading {
            pointer-events: none;
            opacity: 0.8;
        }
        
        .submit-btn .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        .submit-btn.loading .spinner {
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .back-link {
            color: #0C55AA;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-link:hover {
            color: #3D73C4;
            text-decoration: underline;
        }
        
        .back-link svg {
            width: 16px;
            height: 16px;
        }
        
        .success-message {
            display: none;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .success-message.show {
            display: block;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn:hover {
            background: white;
            transform: scale(1.1);
        }
        
        .back-btn svg {
            width: 20px;
            height: 20px;
            color: #0C55AA;
        }
        
        @media (max-width: 480px) {
            .forgot-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .page-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m12 19-7-7 7-7"/>
            <path d="M19 12H5"/>
        </svg>
    </button>

    <div class="forgot-container">
        <div class="icon-section">
            <div class="forgot-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                    <circle cx="12" cy="16" r="1"/>
                    <path d="m7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
            </div>
            <h1 class="page-title">نسيت كلمة المرور؟</h1>
            <p class="page-subtitle">لا تقلق، سنرسل لك رابط إعادة تعيين كلمة المرور على بريدك الإلكتروني</p>
        </div>

        <div class="success-message" id="success-message">
            تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد.
        </div>

        <form id="forgotForm">
            <div class="form-group">
                <label class="form-label" for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" class="form-input" placeholder="أدخل بريدك الإلكتروني" required>
                <div class="error-message" id="email-error"></div>
            </div>

            <button type="submit" class="submit-btn">
                <span class="spinner"></span>
                إرسال رابط الاستعادة
            </button>
        </form>

        <a href="mobile-login.html" class="back-link">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m12 19-7-7 7-7"/>
                <path d="M19 12H5"/>
            </svg>
            العودة لتسجيل الدخول
        </a>
    </div>

    <script>
        // Form handling
        document.getElementById('forgotForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const btn = document.querySelector('.submit-btn');
            const email = document.getElementById('email').value;
            const successMessage = document.getElementById('success-message');
            
            // Clear previous errors
            clearErrors();
            
            // Validate
            if (!email) {
                showError('email', 'يرجى إدخال البريد الإلكتروني');
                return;
            }
            
            if (!isValidEmail(email)) {
                showError('email', 'يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
            
            // Show loading
            btn.classList.add('loading');
            
            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Success - show success message
                successMessage.classList.add('show');
                document.getElementById('forgotForm').style.display = 'none';
                
                // Redirect after 3 seconds
                setTimeout(() => {
                    window.location.href = 'mobile-login.html';
                }, 3000);
                
            } catch (error) {
                showError('email', 'حدث خطأ في إرسال الرابط. يرجى المحاولة مرة أخرى.');
            } finally {
                btn.classList.remove('loading');
            }
        });
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showError(field, message) {
            const input = document.getElementById(field);
            const errorDiv = document.getElementById(field + '-error');
            
            input.classList.add('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function clearErrors() {
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error');
            });
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
            });
        }
        
        function goBack() {
            window.location.href = 'mobile-login.html';
        }
        
        // Clear errors on input
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('input', function() {
                this.classList.remove('error');
                const errorDiv = document.getElementById(this.id + '-error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
