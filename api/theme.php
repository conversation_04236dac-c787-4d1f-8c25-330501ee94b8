<?php

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'sql_dalilakauto_';
$username = 'root';
$password = '';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // جلب إعدادات الثيم
    $stmt = $pdo->prepare("
        SELECT `key`, `value` 
        FROM settings 
        WHERE `key` IN (
            'theme-shofy-primary_color',
            'theme-shofy-tp_primary_font',
            'theme-shofy-logo',
            'theme-shofy-logo_light',
            'theme-shofy-favicon',
            'theme-shofy-site_name',
            'theme-shofy-site_title',
            'admin_primary_color',
            'admin_secondary_color',
            'admin_text_color',
            'admin_link_color',
            'admin_primary_font'
        )
    ");
    
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // دالة لتحويل hex إلى integer للـ Flutter
    function hexToInt($hex) {
        $hex = ltrim($hex, '#');
        return hexdec('FF' . $hex); // إضافة FF للـ alpha channel
    }

    // دالة لتفتيح اللون
    function lightenColor($hex, $percent) {
        $hex = ltrim($hex, '#');
        
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        $r = min(255, $r + ($percent / 100) * (255 - $r));
        $g = min(255, $g + ($percent / 100) * (255 - $g));
        $b = min(255, $b + ($percent / 100) * (255 - $b));
        
        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    // تنسيق البيانات للتطبيق
    $primaryColor = $settings['theme-shofy-primary_color'] ?? '#0C55AA';
    $baseUrl = 'http://localhost:8080';

    $themeData = [
        'colors' => [
            'primary' => $primaryColor,
            'secondary' => $settings['admin_secondary_color'] ?? '#6c7a91',
            'text' => $settings['admin_text_color'] ?? '#182433',
            'link' => $settings['admin_link_color'] ?? '#206bc4',
            'success' => '#28a745',
            'warning' => '#ffc107',
            'danger' => '#dc3545',
            'info' => '#17a2b8',
            'light' => '#f8f9fa',
            'dark' => '#343a40',
        ],
        'fonts' => [
            'primary' => $settings['theme-shofy-tp_primary_font'] ?? 'Roboto',
            'arabic' => $settings['admin_primary_font'] ?? 'Cairo',
        ],
        'branding' => [
            'site_name' => $settings['theme-shofy-site_name'] ?? 'دليل قطع الغيار',
            'site_title' => $settings['theme-shofy-site_title'] ?? 'دليلك لقطع غيار السيارات',
            'logo' => !empty($settings['theme-shofy-logo']) ? $baseUrl . '/storage/' . $settings['theme-shofy-logo'] : '',
            'logo_light' => !empty($settings['theme-shofy-logo_light']) ? $baseUrl . '/storage/' . $settings['theme-shofy-logo_light'] : '',
            'favicon' => !empty($settings['theme-shofy-favicon']) ? $baseUrl . '/storage/' . $settings['theme-shofy-favicon'] : '',
        ],
        'mobile_specific' => [
            'primary_color_hex' => hexToInt($primaryColor),
            'secondary_color_hex' => hexToInt($settings['admin_secondary_color'] ?? '#6c7a91'),
            'text_color_hex' => hexToInt($settings['admin_text_color'] ?? '#182433'),
            'gradient_colors' => [
                $primaryColor,
                lightenColor($primaryColor, 20)
            ],
            'color_palette' => [
                'primary' => $primaryColor,
                'primary_light' => lightenColor($primaryColor, 20),
                'primary_dark' => lightenColor($primaryColor, -20),
                'grey_100' => '#f8f9fa',
                'grey_200' => '#e9ecef',
                'grey_300' => '#dee2e6',
                'grey_400' => '#ced4da',
                'grey_500' => '#adb5bd',
                'grey_600' => '#6c757d',
                'grey_700' => '#495057',
                'grey_800' => '#343a40',
                'grey_900' => '#212529',
            ]
        ],
        'typography' => [
            'font_sizes' => [
                'xs' => 12,
                'sm' => 14,
                'base' => 16,
                'lg' => 18,
                'xl' => 20,
                '2xl' => 24,
                '3xl' => 30,
                '4xl' => 36,
                '5xl' => 48,
            ],
            'font_weights' => [
                'light' => 300,
                'normal' => 400,
                'medium' => 500,
                'semibold' => 600,
                'bold' => 700,
                'extrabold' => 800,
            ]
        ]
    ];

    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'data' => $themeData,
        'message' => 'Theme settings retrieved successfully'
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'error' => $e->getMessage()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to retrieve theme settings',
        'error' => $e->getMessage()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
