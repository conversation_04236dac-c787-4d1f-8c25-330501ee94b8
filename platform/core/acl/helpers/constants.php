<?php

if (! defined('AUTH_MODULE_SCREEN_NAME')) {
    define('AUTH_MODULE_SCREEN_NAME', 'auth');
}

if (! defined('USER_MODULE_SCREEN_NAME')) {
    define('USER_MODULE_SCREEN_NAME', 'user');
}

if (! defined('AUTH_ACTION_AFTER_LOGOUT_SYSTEM')) {
    define('AUTH_ACTION_AFTER_LOGOUT_SYSTEM', 'action_after_logout_system');
}

if (! defined('USER_ACTION_AFTER_UPDATE_PROFILE')) {
    define('USER_ACTION_AFTER_UPDATE_PROFILE', 'action_after_update_profile');
}

if (! defined('USER_ACTION_AFTER_UPDATE_PASSWORD')) {
    define('USER_ACTION_AFTER_UPDATE_PASSWORD', 'action_after_update_password');
}

if (! defined('ACL_FILTER_PROFILE_FORM_TABS')) {
    define('ACL_FILTER_PROFILE_FORM_TABS', 'acl_filter_profile_tabs');
}

if (! defined('ACL_FILTER_PROFILE_FORM_TAB_CONTENTS')) {
    define('ACL_FILTER_PROFILE_FORM_TAB_CONTENTS', 'acl_filter_profile_tab_contents');
}

if (! defined('ACL_ROLE_SUPER_USER')) {
    define('ACL_ROLE_SUPER_USER', 'superuser');
}

if (! defined('ACL_ROLE_MANAGE_SUPERS')) {
    define('ACL_ROLE_MANAGE_SUPERS', 'manage_supers');
}

if (! defined('ACL_FILTER_USER_TABLE_ACTIONS')) {
    define('ACL_FILTER_USER_TABLE_ACTIONS', 'user_table_actions');
}
