<?php

namespace Bo<PERSON>ble\ACL\Providers;

use Bo<PERSON>ble\ACL\Commands\UserCreateCommand;
use Bo<PERSON>ble\ACL\Commands\UserPasswordCommand;
use Botble\Base\Supports\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            UserCreateCommand::class,
            UserPasswordCommand::class,
        ]);
    }
}
