<?php

namespace Bo<PERSON><PERSON>\ACL\Providers;

use Bo<PERSON>ble\ACL\Events\RoleAssignmentEvent;
use Bo<PERSON>ble\ACL\Events\RoleUpdateEvent;
use Bo<PERSON><PERSON>\ACL\Listeners\LoginListener;
use Bo<PERSON><PERSON>\ACL\Listeners\RoleAssignmentListener;
use Bo<PERSON>ble\ACL\Listeners\RoleUpdateListener;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        RoleUpdateEvent::class => [
            RoleUpdateListener::class,
        ],
        RoleAssignmentEvent::class => [
            RoleAssignmentListener::class,
        ],
        Login::class => [
            LoginListener::class,
        ],
    ];
}
