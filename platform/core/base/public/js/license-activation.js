(()=>{"use strict";$((function(){$(document).on("submit",'[data-bb-toggle="activate-license"]',(function(t){t.preventDefault();var e=$(this),o=new FormData(t.currentTarget);Botble.showLoading(e[0]),$httpClient.make().postForm(e.prop("action"),o).then((function(t){var o=t.data;if(Botble.showSuccess(o.message),e.data("reload"))setTimeout((function(){window.location.reload()}),1e3);else{var a=e.data("redirect");a&&window.location.assign(a)}})).finally((function(){Botble.hideLoading(e[0])}))}))}))})();