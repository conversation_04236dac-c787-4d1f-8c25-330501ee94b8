<?php

namespace Database\Seeders\Themes\Beauty;

use Database\Seeders\Themes\Main\ProductSeeder as MainProductSeeder;

class ProductSeeder extends MainProductSeeder
{
    protected function getProducts(): array
    {
        return [
            'Vintage Denim Jacket',
            'Floral Maxi Dress',
            'Leather Ankle Boots',
            'Knit Turtleneck Sweater',
            'Classic Aviator Sunglasses',
            'Tailored Wool Blazer',
            'Bohemian Fringe Handbag',
            'Silk Scarf with Geometric Print',
            'High-Waisted Wide Leg Trousers',
            'Embroidered Boho Blouse',
            'Statement Chunky Necklace',
            'Chic Fedora Hat',
            'Strappy Block Heel Sandals',
            'Velvet Evening Gown',
            'Quilted Crossbody Bag',
            'Distressed Skinny Jeans',
            'Lace-Up Combat Boots',
            'Cotton Striped T-Shirt Dress',
            'Printed Palazzo Pants',
            'Structured Satchel Bag',
            'Off-Shoulder Ruffle Top',
            'Suede Pointed-Toe Pumps',
            'Cropped Cable Knit Sweater',
            'Athleisure Jogger Pants',
            'Leopard Print Midi Skirt',
            'Retro Cat-Eye Sunglasses',
            'Faux Fur Trimmed Coat',
            'Boho Fringed Kimono',
            'Ruffled Wrap Dress',
            'Beaded Evening Clutch',
            'Wide Brim Floppy Hat',
            'Denim Overall Jumpsuit',
            'Embellished Ballet Flats',
            'Pleated Midi Skirt',
            'Velour Tracksuit Set',
            'Geometric Patterned Cardigan',
            'Buckle Detail Ankle Booties',
            'Embroidered Bomber Jacket',
            'Cowl Neck Knit Poncho',
            'Chunky Knit Infinity Scarf',
            'Retro High-Top Sneakers',
            'Faux Leather Leggings',
            'Metallic Pleated Maxi Skirt',
        ];
    }

    protected function getDescriptions(): array
    {
        return [
            'Elevate your street style with this vintage denim jacket, featuring distressed detailing and a relaxed fit for a laid-back vibe.',
            'Make a statement in this floral maxi dress, perfect for summer events or a casual day out. The flowy silhouette and vibrant print will turn heads wherever you go.',
            'Step out in style with these leather ankle boots, crafted with a sleek design and comfortable block heel. Versatile enough to pair with both dresses and jeans.',
            'Stay cozy and chic in this knit turtleneck sweater, featuring a ribbed texture and a timeless design. Perfect for chilly days and nights.',
            'Complete your look with these classic aviator sunglasses, offering a touch of timeless glamour and UV protection for your eyes.',
            'Achieve a polished look with this tailored wool blazer, designed for a flattering fit and versatile enough for both work and casual outings.',
            'Accessorize with this bohemian fringe handbag, featuring intricate details and enough space for your essentials. The perfect boho-chic addition to your collection.',
            'Add a pop of color to your outfit with this silk scarf adorned with a geometric print. Wrap it around your neck or tie it to your handbag for a stylish touch.',
            'Stay on trend with these high-waisted wide-leg trousers, combining comfort and sophistication. Pair them with heels for a refined look.',
            'Embrace bohemian vibes with this embroidered boho blouse, featuring intricate stitching and a relaxed fit. Perfect for a casual day with a touch of style.',
            'Make a bold statement with this chunky necklace, designed to add a touch of glamour to any outfit. The perfect accessory for a night out.',
            'Top off your look with this chic fedora hat, crafted with a classic silhouette and a timeless appeal. A must-have accessory for any fashion-forward wardrobe.',
        ];
    }
}
