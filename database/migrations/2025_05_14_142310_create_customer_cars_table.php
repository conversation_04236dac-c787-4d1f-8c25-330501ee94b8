<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('customer_cars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id');
            $table->foreignId('make_id');
            $table->foreignId('model_id');
            $table->foreignId('year_id');
            $table->string('vin', 50)->nullable();
            $table->string('name', 50)->nullable()->comment('Optional user-given nickname for the car');
            $table->timestamps();
            
            $table->index(['customer_id', 'make_id']);
            $table->unique(['customer_id', 'vin']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customer_cars');
    }
}; 