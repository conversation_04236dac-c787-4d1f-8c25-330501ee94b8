<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_years', function (Blueprint $table) {
            $table->id();
            $table->foreignId('model_id')->constrained('car_models')->onDelete('cascade');
            $table->integer('year');
            $table->text('description')->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
            
            $table->unique(['model_id', 'year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('car_years');
    }
};
