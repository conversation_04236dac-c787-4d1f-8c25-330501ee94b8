<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_otp_verifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->nullable()->constrained('ec_customers')->onDelete('cascade');
            $table->string('phone', 20);
            $table->string('email')->nullable();
            $table->string('otp_code', 255); // مشفر
            $table->enum('type', ['registration', 'login', 'password_reset', 'phone_verification', 'two_factor']);
            $table->timestamp('expires_at');
            $table->timestamp('verified_at')->nullable();
            $table->tinyInteger('attempts')->default(0);
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['phone', 'type']);
            $table->index('expires_at');
            $table->index(['phone', 'type', 'verified_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_otp_verifications');
    }
};
