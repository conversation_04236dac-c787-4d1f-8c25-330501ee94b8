<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_cars', function (Blueprint $table) {
            if (!Schema::hasColumn('customer_cars', 'make_category_id')) {
                $table->foreignId('make_category_id')->nullable()->after('year_id')->constrained('ec_product_categories')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('customer_cars', 'model_category_id')) {
                $table->foreignId('model_category_id')->nullable()->after('make_category_id')->constrained('ec_product_categories')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('customer_cars', 'year_category_id')) {
                $table->foreignId('year_category_id')->nullable()->after('model_category_id')->constrained('ec_product_categories')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('customer_cars', 'notes')) {
                $table->text('notes')->nullable()->after('name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_cars', function (Blueprint $table) {
            $columns = [
                'make_category_id',
                'model_category_id', 
                'year_category_id',
                'notes',
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('customer_cars', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
