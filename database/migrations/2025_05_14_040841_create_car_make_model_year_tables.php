<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إنشاء جدول ماركات السيارات
        Schema::create('car_makes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        // إنشاء جدول موديلات السيارات
        Schema::create('car_models', function (Blueprint $table) {
            $table->id();
            $table->foreignId('make_id')->constrained('car_makes')->onDelete('cascade');
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        // إنشاء جدول سنوات الصنع
        Schema::create('car_years', function (Blueprint $table) {
            $table->id();
            $table->foreignId('model_id')->constrained('car_models')->onDelete('cascade');
            $table->integer('year');
            $table->timestamps();
        });

        // إنشاء جدول العلاقة بين المنتجات والسيارات
        Schema::create('ec_product_car_fitment', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('make_id')->constrained('car_makes')->onDelete('cascade');
            $table->foreignId('model_id')->constrained('car_models')->onDelete('cascade');
            $table->foreignId('year_id')->constrained('car_years')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ec_product_car_fitment');
        Schema::dropIfExists('car_years');
        Schema::dropIfExists('car_models');
        Schema::dropIfExists('car_makes');
    }
}; 