<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ec_customers', function (Blueprint $table) {
            $table->timestamp('phone_verified_at')->nullable()->after('confirmed_at');
            $table->boolean('two_factor_enabled')->default(false)->after('phone_verified_at');
            $table->timestamp('last_otp_sent_at')->nullable()->after('two_factor_enabled');
            $table->string('phone_country_code', 5)->default('+964')->after('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ec_customers', function (Blueprint $table) {
            $table->dropColumn([
                'phone_verified_at',
                'two_factor_enabled', 
                'last_otp_sent_at',
                'phone_country_code'
            ]);
        });
    }
};
